"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useUserContext } from "@/contexts/user-context";
import { useEnhancedCart } from "@/contexts/enhanced-cart-context";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Heart, ShoppingCart, Trash2, Star, ArrowLeft } from "lucide-react";
import { toast } from "sonner";

interface WishlistItem {
  id: string;
  product: {
    id: string;
    name: string;
    slug: string;
    price: number;
    salePrice?: number;
    images: string[];
    avgRating: number;
    reviewCount: number;
    stock: number;
    category: {
      name: string;
    };
  };
  createdAt: string;
}

export default function WishlistPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const {
    wishlist: wishlistItems,
    loading,
    removeFromWishlist,
  } = useUserContext();
  const { quickAdd } = useEnhancedCart();

  const handleRemoveFromWishlist = async (productId: string) => {
    try {
      await removeFromWishlist(productId);
      toast.success("Đã xóa khỏi danh sách yêu thích");
    } catch {
      toast.error("Có lỗi xảy ra khi xóa sản phẩm");
    }
  };

  const handleAddToCart = async (productId: string) => {
    try {
      await quickAdd(productId, 1);
      toast.success("Đã thêm vào giỏ hàng");
    } catch {
      toast.error("Có lỗi xảy ra khi thêm vào giỏ hàng");
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("vi-VN", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  if (!session) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-16">
            <Heart className="h-24 w-24 text-gray-300 mx-auto mb-6" />
            <h2 className="text-2xl font-semibold mb-4">Vui lòng đăng nhập</h2>
            <p className="text-muted-foreground mb-8 max-w-md mx-auto">
              Bạn cần đăng nhập để xem danh sách yêu thích của mình.
            </p>
            <Button
              onClick={() => router.push("/auth/signin?callbackUrl=/wishlist")}
              className="bg-pink-600 hover:bg-pink-700"
            >
              Đăng nhập
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (status === "loading" || loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-64" />
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }, (_, i) => (
                <div key={i} className="bg-white rounded-lg p-4">
                  <div className="h-48 bg-gray-200 rounded mb-4" />
                  <div className="h-4 bg-gray-200 rounded mb-2" />
                  <div className="h-4 bg-gray-200 rounded w-2/3" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <Heart className="h-8 w-8 text-pink-600" />
              Danh sách yêu thích
            </h1>
            <p className="text-muted-foreground">
              {wishlistItems ? wishlistItems.length : 0} sản phẩm trong danh
              sách yêu thích
            </p>
          </div>
        </div>

        {!wishlistItems || wishlistItems.length === 0 ? (
          <div className="text-center py-16">
            <Heart className="h-24 w-24 text-gray-300 mx-auto mb-6" />
            <h2 className="text-2xl font-semibold mb-4">
              Danh sách yêu thích trống
            </h2>
            <p className="text-muted-foreground mb-8 max-w-md mx-auto">
              Bạn chưa có sản phẩm nào trong danh sách yêu thích. Hãy khám phá
              và thêm những sản phẩm bạn yêu thích!
            </p>
            <Link href="/products">
              <Button className="bg-pink-600 hover:bg-pink-700">
                Khám phá sản phẩm
              </Button>
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {wishlistItems &&
              wishlistItems.map((item) => (
                <Card
                  key={item.id}
                  className="group hover:shadow-lg transition-shadow"
                >
                  <CardContent className="p-0">
                    <div className="relative">
                      <Link href={`/products/${item.product.slug}`}>
                        <div className="relative h-64 overflow-hidden rounded-t-lg">
                          <Image
                            src={item.product.images[0]}
                            alt={item.product.name}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                        </div>
                      </Link>

                      {/* Remove from wishlist */}
                      <button
                        onClick={() =>
                          handleRemoveFromWishlist(item.product.id)
                        }
                        className="absolute top-3 right-3 p-2 bg-white rounded-full shadow-md hover:bg-red-50 transition-colors"
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </button>

                      {/* Sale badge */}
                      {item.product.salePrice && (
                        <div className="absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
                          -
                          {Math.round(
                            ((item.product.price - item.product.salePrice) /
                              item.product.price) *
                              100
                          )}
                          %
                        </div>
                      )}

                      {/* Stock status */}
                      {item.product.stock === 0 && (
                        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-t-lg">
                          <span className="text-white font-medium">
                            Hết hàng
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="p-4">
                      <div className="mb-2">
                        <span className="text-xs text-muted-foreground">
                          {item.product.category.name}
                        </span>
                      </div>

                      <Link href={`/products/${item.product.slug}`}>
                        <h3 className="font-semibold text-lg mb-2 line-clamp-2 hover:text-pink-600 transition-colors">
                          {item.product.name}
                        </h3>
                      </Link>

                      {/* Rating */}
                      {item.product.reviewCount > 0 && (
                        <div className="flex items-center gap-1 mb-2">
                          <div className="flex">
                            {Array.from({ length: 5 }, (_, i) => (
                              <Star
                                key={i}
                                className={`h-3 w-3 ${
                                  i < Math.floor(item.product.avgRating)
                                    ? "text-yellow-400 fill-current"
                                    : "text-gray-300"
                                }`}
                              />
                            ))}
                          </div>
                          <span className="text-xs text-muted-foreground">
                            ({item.product.reviewCount})
                          </span>
                        </div>
                      )}

                      {/* Price */}
                      <div className="flex items-center gap-2 mb-3">
                        {item.product.salePrice ? (
                          <>
                            <span className="text-lg font-bold text-pink-600">
                              {formatPrice(item.product.salePrice)}
                            </span>
                            <span className="text-sm text-muted-foreground line-through">
                              {formatPrice(item.product.price)}
                            </span>
                          </>
                        ) : (
                          <span className="text-lg font-bold">
                            {formatPrice(item.product.price)}
                          </span>
                        )}
                      </div>

                      {/* Added date */}
                      <p className="text-xs text-muted-foreground mb-3">
                        Thêm vào: {formatDate(item.createdAt)}
                      </p>

                      {/* Actions */}
                      <div className="space-y-2">
                        <Button
                          onClick={() => handleAddToCart(item.product.id)}
                          disabled={item.product.stock === 0}
                          className="w-full bg-pink-600 hover:bg-pink-700"
                        >
                          <ShoppingCart className="h-4 w-4 mr-2" />
                          {item.product.stock === 0
                            ? "Hết hàng"
                            : "Thêm vào giỏ"}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
          </div>
        )}

        {/* Actions */}
        {wishlistItems && wishlistItems.length > 0 && (
          <div className="mt-8 text-center">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="outline"
                onClick={() => {
                  if (
                    confirm(
                      "Bạn có chắc chắn muốn xóa tất cả sản phẩm khỏi danh sách yêu thích?"
                    )
                  ) {
                    // Clear all wishlist items
                    wishlistItems &&
                      wishlistItems.forEach((item) => {
                        handleRemoveFromWishlist(item.product.id);
                      });
                  }
                }}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Xóa tất cả
              </Button>
              <Link href="/products">
                <Button className="bg-pink-600 hover:bg-pink-700">
                  Tiếp tục mua sắm
                </Button>
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
