"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Mail,
  Phone,
  Building,
  Calendar,
  Search,
  Filter,
  Eye,
  MessageSquare,
  User,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Loader2,
} from "lucide-react";
import { toast } from "sonner";
import { EmailTestDialog } from "@/components/admin/email-test-dialog";

interface Contact {
  id: string;
  name: string;
  email: string;
  phone?: string;
  company?: string;
  service?: string;
  subject?: string;
  message: string;
  status:
    | "NEW"
    | "OPEN"
    | "IN_PROGRESS"
    | "WAITING_CUSTOMER"
    | "RESOLVED"
    | "CLOSED"
    | "SPAM";
  priority: "LOW" | "NORMAL" | "HIGH" | "URGENT";
  source: string;
  createdAt: string;
  updatedAt: string;
  assignedAdmin?: {
    id: string;
    name: string;
    email: string;
  };
  notes: Array<{
    id: string;
    note: string;
    isInternal: boolean;
    createdAt: string;
    admin: {
      id: string;
      name: string;
    };
  }>;
}

interface ContactStats {
  overview: {
    total: number;
    new: number;
    open: number;
    resolved: number;
    urgent: number;
  };
  timeframe: {
    today: number;
    week: number;
    month: number;
  };
}

const statusColors = {
  NEW: "bg-blue-100 text-blue-800",
  OPEN: "bg-yellow-100 text-yellow-800",
  IN_PROGRESS: "bg-purple-100 text-purple-800",
  WAITING_CUSTOMER: "bg-orange-100 text-orange-800",
  RESOLVED: "bg-green-100 text-green-800",
  CLOSED: "bg-gray-100 text-gray-800",
  SPAM: "bg-red-100 text-red-800",
};

const priorityColors = {
  LOW: "bg-gray-100 text-gray-800",
  NORMAL: "bg-blue-100 text-blue-800",
  HIGH: "bg-orange-100 text-orange-800",
  URGENT: "bg-red-100 text-red-800",
};

export default function ContactsPage() {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [stats, setStats] = useState<ContactStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [filters, setFilters] = useState({
    search: "",
    status: "",
    priority: "",
    page: 1,
    limit: 10,
  });

  const fetchContacts = async () => {
    try {
      const params = new URLSearchParams();
      if (filters.search) params.append("search", filters.search);
      if (filters.status) params.append("status", filters.status);
      if (filters.priority) params.append("priority", filters.priority);
      params.append("page", filters.page.toString());
      params.append("limit", filters.limit.toString());

      const response = await fetch(`/api/admin/contacts?${params}`);
      if (!response.ok) throw new Error("Failed to fetch contacts");

      const data = await response.json();
      setContacts(data.contacts);
    } catch (error) {
      console.error("Error fetching contacts:", error);
      toast.error("Không thể tải danh sách liên hệ");
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch("/api/admin/contacts/stats");
      if (!response.ok) throw new Error("Failed to fetch stats");

      const data = await response.json();
      setStats(data);
    } catch (error) {
      console.error("Error fetching stats:", error);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchContacts(), fetchStats()]);
      setLoading(false);
    };

    loadData();
  }, [filters]);

  const updateContactStatus = async (contactId: string, status: string) => {
    try {
      const response = await fetch(`/api/admin/contacts/${contactId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) throw new Error("Failed to update contact");

      toast.success("Cập nhật trạng thái thành công");
      fetchContacts();
    } catch (error) {
      console.error("Error updating contact:", error);
      toast.error("Không thể cập nhật trạng thái");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("vi-VN", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Quản lý liên hệ</h1>
          <p className="text-muted-foreground">
            Quản lý và phản hồi các tin nhắn liên hệ từ khách hàng
          </p>
        </div>
        <EmailTestDialog />
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Tổng số</p>
                  <p className="text-2xl font-bold">{stats.overview.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Mới</p>
                  <p className="text-2xl font-bold">{stats.overview.new}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-yellow-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Đang xử lý</p>
                  <p className="text-2xl font-bold">{stats.overview.open}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Đã giải quyết</p>
                  <p className="text-2xl font-bold">
                    {stats.overview.resolved}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <XCircle className="h-4 w-4 text-red-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Khẩn cấp</p>
                  <p className="text-2xl font-bold">{stats.overview.urgent}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Tìm kiếm theo tên, email, công ty..."
                  value={filters.search}
                  onChange={(e) =>
                    setFilters({ ...filters, search: e.target.value, page: 1 })
                  }
                  className="pl-10"
                />
              </div>
            </div>

            <Select
              value={filters.status}
              onValueChange={(value) =>
                setFilters({ ...filters, status: value, page: 1 })
              }
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Trạng thái" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Tất cả trạng thái</SelectItem>
                <SelectItem value="NEW">Mới</SelectItem>
                <SelectItem value="OPEN">Đang mở</SelectItem>
                <SelectItem value="IN_PROGRESS">Đang xử lý</SelectItem>
                <SelectItem value="WAITING_CUSTOMER">Chờ khách hàng</SelectItem>
                <SelectItem value="RESOLVED">Đã giải quyết</SelectItem>
                <SelectItem value="CLOSED">Đã đóng</SelectItem>
                <SelectItem value="SPAM">Spam</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.priority}
              onValueChange={(value) =>
                setFilters({ ...filters, priority: value, page: 1 })
              }
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Độ ưu tiên" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Tất cả độ ưu tiên</SelectItem>
                <SelectItem value="LOW">Thấp</SelectItem>
                <SelectItem value="NORMAL">Bình thường</SelectItem>
                <SelectItem value="HIGH">Cao</SelectItem>
                <SelectItem value="URGENT">Khẩn cấp</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Contacts Table */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách liên hệ</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Khách hàng</TableHead>
                <TableHead>Liên hệ</TableHead>
                <TableHead>Dịch vụ</TableHead>
                <TableHead>Trạng thái</TableHead>
                <TableHead>Độ ưu tiên</TableHead>
                <TableHead>Ngày tạo</TableHead>
                <TableHead>Thao tác</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {contacts.map((contact) => (
                <TableRow key={contact.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{contact.name}</div>
                      {contact.company && (
                        <div className="text-sm text-muted-foreground flex items-center gap-1">
                          <Building className="h-3 w-3" />
                          {contact.company}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center gap-1 text-sm">
                        <Mail className="h-3 w-3" />
                        {contact.email}
                      </div>
                      {contact.phone && (
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <Phone className="h-3 w-3" />
                          {contact.phone}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      {contact.service && (
                        <div className="text-sm">{contact.service}</div>
                      )}
                      {contact.subject && (
                        <div className="text-sm text-muted-foreground">
                          {contact.subject}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Select
                      value={contact.status}
                      onValueChange={(value) =>
                        updateContactStatus(contact.id, value)
                      }
                    >
                      <SelectTrigger className="w-[140px]">
                        <Badge className={statusColors[contact.status]}>
                          {contact.status}
                        </Badge>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="NEW">Mới</SelectItem>
                        <SelectItem value="OPEN">Đang mở</SelectItem>
                        <SelectItem value="IN_PROGRESS">Đang xử lý</SelectItem>
                        <SelectItem value="WAITING_CUSTOMER">
                          Chờ khách hàng
                        </SelectItem>
                        <SelectItem value="RESOLVED">Đã giải quyết</SelectItem>
                        <SelectItem value="CLOSED">Đã đóng</SelectItem>
                        <SelectItem value="SPAM">Spam</SelectItem>
                      </SelectContent>
                    </Select>
                  </TableCell>
                  <TableCell>
                    <Badge className={priorityColors[contact.priority]}>
                      {contact.priority}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1 text-sm">
                      <Calendar className="h-3 w-3" />
                      {formatDate(contact.createdAt)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedContact(contact)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle>Chi tiết liên hệ</DialogTitle>
                        </DialogHeader>
                        {selectedContact && (
                          <div className="space-y-4">
                            {/* Contact Info */}
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <label className="text-sm font-medium">
                                  Họ tên
                                </label>
                                <p className="text-sm">
                                  {selectedContact.name}
                                </p>
                              </div>
                              <div>
                                <label className="text-sm font-medium">
                                  Email
                                </label>
                                <p className="text-sm">
                                  {selectedContact.email}
                                </p>
                              </div>
                              {selectedContact.phone && (
                                <div>
                                  <label className="text-sm font-medium">
                                    Điện thoại
                                  </label>
                                  <p className="text-sm">
                                    {selectedContact.phone}
                                  </p>
                                </div>
                              )}
                              {selectedContact.company && (
                                <div>
                                  <label className="text-sm font-medium">
                                    Công ty
                                  </label>
                                  <p className="text-sm">
                                    {selectedContact.company}
                                  </p>
                                </div>
                              )}
                            </div>

                            {/* Service & Subject */}
                            {selectedContact.service && (
                              <div>
                                <label className="text-sm font-medium">
                                  Dịch vụ quan tâm
                                </label>
                                <p className="text-sm">
                                  {selectedContact.service}
                                </p>
                              </div>
                            )}
                            {selectedContact.subject && (
                              <div>
                                <label className="text-sm font-medium">
                                  Chủ đề
                                </label>
                                <p className="text-sm">
                                  {selectedContact.subject}
                                </p>
                              </div>
                            )}

                            {/* Message */}
                            <div>
                              <label className="text-sm font-medium">
                                Tin nhắn
                              </label>
                              <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                                <p className="text-sm whitespace-pre-wrap">
                                  {selectedContact.message}
                                </p>
                              </div>
                            </div>

                            {/* Status & Priority */}
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <label className="text-sm font-medium">
                                  Trạng thái
                                </label>
                                <div className="mt-1">
                                  <Badge
                                    className={
                                      statusColors[selectedContact.status]
                                    }
                                  >
                                    {selectedContact.status}
                                  </Badge>
                                </div>
                              </div>
                              <div>
                                <label className="text-sm font-medium">
                                  Độ ưu tiên
                                </label>
                                <div className="mt-1">
                                  <Badge
                                    className={
                                      priorityColors[selectedContact.priority]
                                    }
                                  >
                                    {selectedContact.priority}
                                  </Badge>
                                </div>
                              </div>
                            </div>

                            {/* Timestamps */}
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <label className="text-sm font-medium">
                                  Ngày tạo
                                </label>
                                <p className="text-sm">
                                  {formatDate(selectedContact.createdAt)}
                                </p>
                              </div>
                              <div>
                                <label className="text-sm font-medium">
                                  Cập nhật cuối
                                </label>
                                <p className="text-sm">
                                  {formatDate(selectedContact.updatedAt)}
                                </p>
                              </div>
                            </div>

                            {/* Notes */}
                            {selectedContact.notes.length > 0 && (
                              <div>
                                <label className="text-sm font-medium">
                                  Ghi chú
                                </label>
                                <div className="mt-2 space-y-2">
                                  {selectedContact.notes.map((note) => (
                                    <div
                                      key={note.id}
                                      className="p-3 bg-gray-50 rounded-lg"
                                    >
                                      <div className="flex items-center justify-between mb-1">
                                        <span className="text-xs font-medium">
                                          {note.admin.name}
                                        </span>
                                        <span className="text-xs text-muted-foreground">
                                          {formatDate(note.createdAt)}
                                        </span>
                                      </div>
                                      <p className="text-sm">{note.note}</p>
                                      {note.isInternal && (
                                        <Badge
                                          variant="secondary"
                                          className="mt-1 text-xs"
                                        >
                                          Nội bộ
                                        </Badge>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </DialogContent>
                    </Dialog>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {contacts.length === 0 && (
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">Không có liên hệ nào</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
