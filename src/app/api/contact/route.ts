import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { emailService } from "@/lib/email-service";
import type { ContactFormEmailData } from "@/lib/email/templates";
import { prisma } from "@/lib/prisma";

const contactFormSchema = z.object({
  name: z.string().min(2, "Tên phải có ít nhất 2 ký tự"),
  email: z.string().email("Email không hợp lệ"),
  phone: z.string().optional(),
  company: z.string().optional(),
  service: z.string().optional(),
  subject: z.string().min(5, "Chủ đề phải có ít nhất 5 ký tự"),
  message: z.string().min(10, "Tin nhắn phải có ít nhất 10 ký tự"),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const data = contactFormSchema.parse(body);

    // Get client IP and user agent
    const ipAddress =
      request.headers.get("x-forwarded-for") ||
      request.headers.get("x-real-ip") ||
      request.ip ||
      "unknown";
    const userAgent = request.headers.get("user-agent") || "unknown";

    // Save contact to database
    const contact = await prisma.contact.create({
      data: {
        name: data.name,
        email: data.email,
        phone: data.phone,
        company: data.company,
        service: data.service,
        subject: data.subject,
        message: data.message,
        source: "website",
        ipAddress,
        userAgent,
        status: "NEW",
        priority: "NORMAL",
      },
    });

    // Initialize email service
    const initialized = await emailService.initialize();
    if (initialized) {
      // Prepare enhanced contact form email data
      const contactFormData: ContactFormEmailData = {
        recipientName: "NS Shop Admin",
        recipientEmail:
          process.env.ADMIN_EMAIL ||
          process.env.EMAIL_FROM ||
          "<EMAIL>",
        senderName: data.name,
        senderEmail: data.email,
        senderPhone: data.phone,
        subject: data.subject,
        message: data.message,
        submittedAt: new Date().toISOString(),
        // Add additional contact info
        company: data.company,
        service: data.service,
        contactId: contact.id,
        source: "website",
        ipAddress,
        userAgent,
      };

      // Send contact form email (non-blocking)
      emailService.sendContactFormEmail(contactFormData).catch((error) => {
        console.error("Failed to send contact email:", error);
      });
    }

    return NextResponse.json(
      {
        message:
          "Gửi tin nhắn thành công! Chúng tôi sẽ phản hồi trong thời gian sớm nhất.",
        success: true,
        contactId: contact.id,
      },
      { status: 200 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Contact form error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi gửi tin nhắn" },
      { status: 500 }
    );
  }
}
