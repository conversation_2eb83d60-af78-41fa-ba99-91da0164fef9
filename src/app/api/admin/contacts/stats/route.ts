import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// GET /api/admin/contacts/stats - Get contact statistics
export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 });
    }

    const now = new Date();
    const startOfToday = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate()
    );
    const startOfWeek = new Date(startOfToday);
    startOfWeek.setDate(startOfToday.getDate() - startOfToday.getDay());
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Get overall stats
    const [
      totalContacts,
      newContacts,
      openContacts,
      resolvedContacts,
      todayContacts,
      weekContacts,
      monthContacts,
      urgentContacts,
    ] = await Promise.all([
      prisma.contact.count(),
      prisma.contact.count({ where: { status: "NEW" } }),
      prisma.contact.count({
        where: { status: { in: ["OPEN", "IN_PROGRESS"] } },
      }),
      prisma.contact.count({ where: { status: "RESOLVED" } }),
      prisma.contact.count({ where: { createdAt: { gte: startOfToday } } }),
      prisma.contact.count({ where: { createdAt: { gte: startOfWeek } } }),
      prisma.contact.count({ where: { createdAt: { gte: startOfMonth } } }),
      prisma.contact.count({ where: { priority: "URGENT" } }),
    ]);

    // Get status distribution
    const statusStats = await prisma.contact.groupBy({
      by: ["status"],
      _count: {
        status: true,
      },
    });

    // Get priority distribution
    const priorityStats = await prisma.contact.groupBy({
      by: ["priority"],
      _count: {
        priority: true,
      },
    });

    // Get source distribution
    const sourceStats = await prisma.contact.groupBy({
      by: ["source"],
      _count: {
        source: true,
      },
    });

    // Get daily contacts for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const dailyContacts = await prisma.$queryRaw<
      Array<{ date: string; count: bigint }>
    >`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count
      FROM contacts 
      WHERE created_at >= ${thirtyDaysAgo}
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `;

    // Get top services requested
    const serviceStats = await prisma.contact.groupBy({
      by: ["service"],
      _count: {
        service: true,
      },
      where: {
        service: {
          not: null,
        },
      },
      orderBy: {
        _count: {
          service: "desc",
        },
      },
      take: 10,
    });

    // Get admin assignment stats
    const assignmentStats = await prisma.contact.groupBy({
      by: ["assignedTo"],
      _count: {
        assignedTo: true,
      },
      where: {
        assignedTo: {
          not: null,
        },
      },
    });

    // Get admin details for assignment stats
    const adminIds = assignmentStats
      .map((stat) => stat.assignedTo)
      .filter(Boolean);
    const admins = await prisma.adminUser.findMany({
      where: {
        id: {
          in: adminIds as string[],
        },
      },
      select: {
        id: true,
        name: true,
      },
    });

    const assignmentStatsWithNames = assignmentStats.map((stat) => ({
      adminId: stat.assignedTo,
      adminName:
        admins.find((admin) => admin.id === stat.assignedTo)?.name || "Unknown",
      count: stat._count.assignedTo,
    }));

    // Calculate response time stats
    const responseTimeStats = await prisma.contact.aggregate({
      where: {
        respondedAt: {
          not: null,
        },
      },
      _avg: {
        // This would need a computed field for response time in hours
        // For now, we'll calculate it differently
      },
    });

    return NextResponse.json({
      overview: {
        total: totalContacts,
        new: newContacts,
        open: openContacts,
        resolved: resolvedContacts,
        urgent: urgentContacts,
      },
      timeframe: {
        today: todayContacts,
        week: weekContacts,
        month: monthContacts,
      },
      distribution: {
        status: statusStats.map((stat) => ({
          status: stat.status,
          count: stat._count.status,
        })),
        priority: priorityStats.map((stat) => ({
          priority: stat.priority,
          count: stat._count.priority,
        })),
        source: sourceStats.map((stat) => ({
          source: stat.source,
          count: stat._count.source,
        })),
      },
      trends: {
        daily: dailyContacts.map((day) => ({
          date: day.date,
          count: Number(day.count),
        })),
      },
      services: serviceStats.map((stat) => ({
        service: stat.service,
        count: stat._count.service,
      })),
      assignments: assignmentStatsWithNames,
    });
  } catch (error) {
    console.error("Error fetching contact stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
