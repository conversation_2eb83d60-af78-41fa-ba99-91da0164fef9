"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import { ClientImage } from "@/components/ui/client-image";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Slider } from "@/components/ui/slider";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Separator } from "@/components/ui/separator";
import {
  Grid,
  List,
  Star,
  Heart,
  ShoppingCart,
  X,
  SlidersHorizontal,
  Loader2,
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { useCategories } from "@/hooks";
import { useSearchContext, useEnhancedCart, useUserContext } from "@/contexts";

interface ProductFilters {
  categoryId?: string;
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  featured?: boolean;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export function EnhancedProductsPage() {
  const searchParams = useSearchParams();
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showFilters, setShowFilters] = useState(false);
  const [priceRange, setPriceRange] = useState([0, 5000000]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");

  // Get search query from URL params
  const searchQuery = searchParams.get("q") || "";
  const categoryParam = searchParams.get("category");

  // Use hooks
  const { data: categories } = useCategories();
  const {
    query,
    setQuery,
    advancedSearch,
    advancedResults,
    advancedLoading,
    filters: _filters,
    setFilters,
  } = useSearchContext();
  const { quickAdd, quickAddLoading, isInCart } = useEnhancedCart();
  const { addToWishlist, isInWishlist } = useUserContext();

  // Initialize search query and category from URL
  useEffect(() => {
    if (searchQuery && searchQuery !== query) {
      setQuery(searchQuery);
    }
    if (categoryParam && !selectedCategories.includes(categoryParam)) {
      setSelectedCategories([categoryParam]);
    }
  }, [searchQuery, categoryParam, query, setQuery, selectedCategories]);

  // Perform search when filters change
  useEffect(() => {
    const searchFilters: ProductFilters = {
      categoryId:
        selectedCategories.length > 0 ? selectedCategories[0] : undefined,
      minPrice: priceRange[0],
      maxPrice: priceRange[1],
      sortBy,
      sortOrder,
    };

    setFilters(searchFilters);

    // Always perform search, even without query to show all products
    advancedSearch({
      query: query || undefined,
      ...searchFilters,
    });
  }, [
    query,
    selectedCategories,
    priceRange,
    sortBy,
    sortOrder,
    setFilters,
    advancedSearch,
  ]);

  const handleCategoryToggle = (categoryId: string) => {
    setSelectedCategories((prev) =>
      prev.includes(categoryId)
        ? prev.filter((id) => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const clearFilters = () => {
    setSelectedCategories([]);
    setPriceRange([0, 5000000]);
    setSortBy("name");
    setSortOrder("asc");
  };

  const products = advancedResults?.products || [];
  const totalProducts = advancedResults?.total || 0;

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              {query ? `Kết quả tìm kiếm: "${query}"` : "Tất cả sản phẩm"}
            </h1>
            <p className="text-muted-foreground">
              Tìm thấy {totalProducts} sản phẩm
            </p>
          </div>

          {/* View Controls */}
          <div className="flex items-center gap-4 mt-4 lg:mt-0">
            <div className="flex items-center border rounded-lg p-1">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>

            {/* Mobile Filter Button */}
            <Sheet open={showFilters} onOpenChange={setShowFilters}>
              <SheetTrigger asChild>
                <Button variant="outline" className="lg:hidden">
                  <SlidersHorizontal className="h-4 w-4 mr-2" />
                  Bộ lọc
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-80">
                <SheetHeader>
                  <SheetTitle>Bộ lọc sản phẩm</SheetTitle>
                </SheetHeader>
                <div className="mt-6">
                  {/* Filter content will be added here */}
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>

        <div className="flex gap-8">
          {/* Sidebar Filters - Desktop */}
          <div className="hidden lg:block w-80 space-y-6">
            <div className="bg-card p-6 rounded-lg border">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold">Bộ lọc</h3>
                <Button variant="ghost" size="sm" onClick={clearFilters}>
                  <X className="h-4 w-4 mr-1" />
                  Xóa
                </Button>
              </div>

              {/* Categories */}
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-3">Danh mục</h4>
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {categories &&
                      categories.map((category) => (
                        <div
                          key={category.id}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={category.id}
                            checked={selectedCategories.includes(category.id)}
                            onCheckedChange={() =>
                              handleCategoryToggle(category.id)
                            }
                          />
                          <label
                            htmlFor={category.id}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {category.name}
                          </label>
                        </div>
                      ))}
                  </div>
                </div>

                <Separator />

                {/* Price Range */}
                <div>
                  <h4 className="font-medium mb-3">Khoảng giá</h4>
                  <div className="space-y-4">
                    <Slider
                      value={priceRange}
                      onValueChange={setPriceRange}
                      max={5000000}
                      step={100000}
                      className="w-full"
                    />
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <span>{formatCurrency(priceRange[0])}</span>
                      <span>{formatCurrency(priceRange[1])}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="flex-1">
            {/* Sort Controls */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="border rounded-lg px-3 py-2 text-sm"
                >
                  <option value="name">Tên sản phẩm</option>
                  <option value="price">Giá</option>
                  <option value="createdAt">Mới nhất</option>
                  <option value="avgRating">Đánh giá</option>
                </select>
                <select
                  value={sortOrder}
                  onChange={(e) =>
                    setSortOrder(e.target.value as "asc" | "desc")
                  }
                  className="border rounded-lg px-3 py-2 text-sm"
                >
                  <option value="asc">Tăng dần</option>
                  <option value="desc">Giảm dần</option>
                </select>
              </div>
            </div>

            {/* Loading State */}
            {advancedLoading && (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2 text-muted-foreground">
                  Đang tải sản phẩm...
                </span>
              </div>
            )}

            {/* Products Grid */}
            {!advancedLoading && products.length > 0 && (
              <div
                className={`grid gap-6 ${
                  viewMode === "grid"
                    ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
                    : "grid-cols-1"
                }`}
              >
                {products.map((product) => (
                  <Card
                    key={product.id}
                    className={`group hover:shadow-lg transition-shadow ${
                      viewMode === "list" ? "flex" : ""
                    }`}
                  >
                    <Link href={`/products/${product.slug}`} className="block">
                      <div
                        className={`relative overflow-hidden ${
                          viewMode === "list"
                            ? "w-48 h-48 flex-shrink-0"
                            : "aspect-square"
                        } rounded-t-lg`}
                      >
                        <ClientImage
                          src={product.images?.[0] || "/images/placeholder.jpg"}
                          alt={product.name}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                          fallbackSrc="/images/placeholder.jpg"
                        />
                        {product.salePrice && (
                          <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
                            -
                            {Math.round(
                              ((product.price - product.salePrice) /
                                product.price) *
                                100
                            )}
                            %
                          </div>
                        )}

                        {/* Actions */}
                        <div className="absolute top-2 right-2 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <Button
                            size="icon"
                            variant="secondary"
                            className="h-8 w-8"
                            onClick={(e) => {
                              e.preventDefault();
                              addToWishlist(product.id);
                            }}
                          >
                            <Heart
                              className={`h-4 w-4 ${
                                isInWishlist(product.id)
                                  ? "fill-red-500 text-red-500"
                                  : ""
                              }`}
                            />
                          </Button>
                        </div>

                        {/* Quick Add to Cart */}
                        <div className="absolute bottom-2 left-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <Button
                            className="w-full"
                            size="sm"
                            disabled={quickAddLoading || isInCart(product.id)}
                            onClick={(e) => {
                              e.preventDefault();
                              quickAdd(product.id);
                            }}
                          >
                            <ShoppingCart className="h-4 w-4 mr-2" />
                            {isInCart(product.id)
                              ? "Đã có trong giỏ"
                              : "Thêm vào giỏ"}
                          </Button>
                        </div>
                      </div>
                    </Link>

                    {/* Product Info */}
                    <div className="p-4 flex-1">
                      <Link href={`/products/${product.slug}`}>
                        <h3 className="font-semibold text-sm lg:text-base mb-2 hover:text-primary transition-colors line-clamp-2">
                          {product.name}
                        </h3>
                      </Link>

                      {/* Rating */}
                      <div className="flex items-center gap-1 mb-2">
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-3 w-3 ${
                                i < Math.floor(product.avgRating || 0)
                                  ? "fill-yellow-400 text-yellow-400"
                                  : "text-gray-300"
                              }`}
                            />
                          ))}
                        </div>
                        <span className="text-xs text-muted-foreground">
                          ({product.reviewCount || 0})
                        </span>
                      </div>

                      {/* Price */}
                      <div className="flex items-center gap-2">
                        <span className="font-bold text-primary">
                          {formatCurrency(product.salePrice || product.price)}
                        </span>
                        {product.salePrice && (
                          <span className="text-sm text-muted-foreground line-through">
                            {formatCurrency(product.price)}
                          </span>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}

            {/* No Results */}
            {!advancedLoading && products.length === 0 && (
              <div className="text-center py-12">
                <p className="text-muted-foreground">
                  Không tìm thấy sản phẩm nào.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
