"use client";

import Link from "next/link";
import {
  Heart,
  ShoppingCart,
  Star,
  ArrowRight,
  Eye,
  Loader2,
  AlertCircle,
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ClientImage } from "@/components/ui/client-image";
import { formatCurrency } from "@/lib/utils";
import { useFeaturedProducts } from "@/hooks";
import { useEnhancedCart, useUserContext } from "@/contexts";

// Helper function to get product image
function getProductImage(product: any): string {
  // Check for new media relationship first
  if (product.media && product.media.length > 0) {
    const primaryImage =
      product.media.find((m: any) => m.isPrimary) || product.media[0];
    return primaryImage.media.url;
  }

  // Fallback to legacy images field
  if (product.images && product.images.length > 0) {
    return product.images[0];
  }

  // Default fallback
  return "/images/placeholder.svg";
}

// Helper function to determine if product is new (created within last 30 days)
function isNewProduct(createdAt: string): boolean {
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  return new Date(createdAt) > thirtyDaysAgo;
}

const _sampleFeaturedProducts = [
  {
    id: 1,
    name: "Áo thun cotton premium",
    slug: "ao-thun-cotton-premium",
    price: 299000,
    salePrice: 199000,
    image: "/images/products/ao-thun-cotton-premium.svg",
    rating: 4.8,
    reviews: 124,
    isNew: true,
    isSale: true,
    colors: ["#000000", "#FFFFFF", "#808080"],
  },
  {
    id: 2,
    name: "Váy maxi hoa nhí",
    slug: "vay-maxi-hoa-nhi",
    price: 599000,
    salePrice: null,
    image: "/images/products/vay-maxi-hoa-nhi.svg",
    rating: 4.9,
    reviews: 89,
    isNew: false,
    isSale: false,
    colors: ["#FFB6C1", "#87CEEB", "#98FB98"],
  },
  {
    id: 3,
    name: "Quần jeans skinny",
    slug: "quan-jeans-skinny",
    price: 799000,
    salePrice: 599000,
    image: "/images/products/quan-jeans-skinny.svg",
    rating: 4.7,
    reviews: 156,
    isNew: false,
    isSale: true,
    colors: ["#4169E1", "#000080", "#191970"],
  },
  {
    id: 4,
    name: "Áo khoác blazer",
    slug: "ao-khoac-blazer",
    price: 1299000,
    salePrice: null,
    image: "/images/products/ao-khoac-blazer.svg",
    rating: 4.6,
    reviews: 67,
    isNew: true,
    isSale: false,
    colors: ["#000000", "#8B4513", "#2F4F4F"],
  },
  {
    id: 5,
    name: "Túi xách da thật",
    slug: "tui-xach-da-that",
    price: 899000,
    salePrice: 699000,
    image: "/images/products/tui-xach-da-that.svg",
    rating: 4.9,
    reviews: 203,
    isNew: false,
    isSale: true,
    colors: ["#8B4513", "#000000", "#CD853F"],
  },
  {
    id: 6,
    name: "Giày sneaker trắng",
    slug: "giay-sneaker-trang",
    price: 1199000,
    salePrice: null,
    image: "/images/products/giay-sneaker-trang.svg",
    rating: 4.8,
    reviews: 145,
    isNew: true,
    isSale: false,
    colors: ["#FFFFFF", "#000000", "#C0C0C0"],
  },
];

export function ModernFeaturedProducts() {
  const { products, loading, error } = useFeaturedProducts(6);
  const { quickAdd, quickAddLoading, isInCart } = useEnhancedCart();
  const { addToWishlist, isInWishlist } = useUserContext();

  return (
    <section className="py-20 lg:py-32 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-white rounded-full px-4 py-2 mb-6 shadow-sm">
            <div className="w-2 h-2 bg-black rounded-full"></div>
            <span className="text-sm font-medium text-gray-700">
              Sản phẩm nổi bật
            </span>
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Được yêu thích nhất
            <br />
            <span className="text-gray-600">trong tuần này</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Những sản phẩm được khách hàng đánh giá cao và mua nhiều nhất trong
            thời gian gần đây.
          </p>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-muted-foreground">
              Đang tải sản phẩm...
            </span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="flex justify-center items-center py-12">
            <AlertCircle className="h-8 w-8 text-red-500" />
            <span className="ml-2 text-red-500">
              Có lỗi xảy ra khi tải sản phẩm
            </span>
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && products && products.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">
              Chưa có sản phẩm nổi bật nào
            </p>
          </div>
        )}

        {/* Products Grid */}
        {!loading && !error && products && products.length > 0 && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {products.map((product) => (
              <Card
                key={product.id}
                className="group overflow-hidden border-0 bg-white hover:shadow-2xl transition-all duration-500"
              >
                <CardContent className="p-0">
                  {/* Product Image */}
                  <div className="relative overflow-hidden">
                    <div className="aspect-[4/5] relative bg-gray-100">
                      <ClientImage
                        src={getProductImage(product)}
                        alt={product.name}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-700"
                        fallbackSrc="/images/placeholder.jpg"
                      />

                      {/* Overlay */}
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-500" />

                      {/* Badges */}
                      <div className="absolute top-4 left-4 flex flex-col gap-2">
                        {isNewProduct(product.createdAt.toString()) && (
                          <Badge className="bg-black text-white border-0 px-2 py-1 text-xs">
                            Mới
                          </Badge>
                        )}
                        {product.salePrice && (
                          <Badge className="bg-red-500 text-white border-0 px-2 py-1 text-xs">
                            -
                            {Math.round(
                              ((product.price -
                                (product.salePrice || product.price)) /
                                product.price) *
                                100
                            )}
                            %
                          </Badge>
                        )}
                      </div>

                      {/* Quick Actions */}
                      <div className="absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <Button
                          size="icon"
                          variant="secondary"
                          className="h-8 w-8 bg-white/90 hover:bg-white"
                          onClick={(e) => {
                            e.preventDefault();
                            addToWishlist(product.id);
                          }}
                        >
                          <Heart
                            className={`h-4 w-4 ${
                              isInWishlist(product.id)
                                ? "fill-red-500 text-red-500"
                                : ""
                            }`}
                          />
                        </Button>
                        <Button
                          size="icon"
                          variant="secondary"
                          className="h-8 w-8 bg-white/90 hover:bg-white"
                          asChild
                        >
                          <Link href={`/products/${product.slug}`}>
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>
                      </div>

                      {/* Add to Cart Button */}
                      <div className="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <Button
                          className="w-full bg-black hover:bg-gray-800 text-white"
                          disabled={quickAddLoading || isInCart(product.id)}
                          onClick={(e) => {
                            e.preventDefault();
                            quickAdd(product.id);
                          }}
                        >
                          <ShoppingCart className="h-4 w-4 mr-2" />
                          {isInCart(product.id)
                            ? "Đã có trong giỏ"
                            : "Thêm vào giỏ"}
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Product Info */}
                  <div className="p-6">
                    <Link href={`/products/${product.slug}`}>
                      <h3 className="font-semibold text-lg mb-2 hover:text-gray-600 transition-colors line-clamp-2">
                        {product.name}
                      </h3>
                    </Link>

                    {/* Rating */}
                    <div className="flex items-center gap-2 mb-3">
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < Math.floor(product.avgRating)
                                ? "text-yellow-400 fill-current"
                                : "text-gray-300"
                            }`}
                          />
                        ))}
                      </div>
                      <span className="text-sm text-gray-600">
                        {product.avgRating.toFixed(1)} ({product.reviewCount})
                      </span>
                    </div>

                    {/* Category */}
                    <div className="flex items-center gap-2 mb-4">
                      <span className="text-sm text-gray-600">Danh mục:</span>
                      <span className="text-sm font-medium text-gray-900">
                        {product.category.name}
                      </span>
                    </div>

                    {/* Price */}
                    <div className="flex items-center gap-3">
                      <span className="text-xl font-bold text-gray-900">
                        {formatCurrency(product.salePrice || product.price)}
                      </span>
                      {product.salePrice && (
                        <span className="text-sm text-gray-500 line-through">
                          {formatCurrency(product.price)}
                        </span>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* View All Products */}
        {!loading && !error && products && products.length > 0 && (
          <div className="text-center mt-16">
            <Link
              href="/products"
              className="inline-flex items-center gap-3 bg-black text-white px-8 py-4 rounded-full font-medium hover:bg-gray-800 transition-colors group"
            >
              Xem tất cả sản phẩm
              <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Link>
          </div>
        )}
      </div>
    </section>
  );
}
