"use client";

import { motion } from "framer-motion";
import {
  Phone,
  Mail,
  MessageCircle,
  Clock,
  MapPin,
  Send,
  User,
  MessageSquare,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useState } from "react";
import { toast } from "sonner";
import {
  useContactInfo,
  useContactActions,
  useFormattedAddress,
} from "@/hooks/use-contact-info";

export function FAQContactSection() {
  const contactData = useContactInfo();
  const contactActions = useContactActions();
  const formattedAddress = useFormattedAddress();

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const contactMethods = [
    {
      icon: Phone,
      title: "Hotline",
      content: contactData.phone,
      description: "Hỗ trợ 24/7",
      color: "green",
      action: contactActions.callPhone,
    },
    {
      icon: Mail,
      title: "Email",
      content: contactData.email,
      description: "Phản hồi trong 2h",
      color: "blue",
      action: () => contactActions.sendEmail("Câu hỏi từ trang FAQ"),
    },
    {
      icon: MessageCircle,
      title: "Zalo",
      content: contactData.phone,
      description: "Chat trực tiếp",
      color: "blue",
      action: () =>
        contactActions.openZalo("Xin chào! Tôi có câu hỏi cần được hỗ trợ."),
    },
    {
      icon: MapPin,
      title: "Địa chỉ",
      content: formattedAddress.shortAddress,
      description: "Ghé thăm showroom",
      color: "purple",
      action: contactActions.openGoogleMaps,
    },
  ];

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch("/api/contact", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          source: "faq-page",
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success(
          "Gửi câu hỏi thành công! Chúng tôi sẽ phản hồi sớm nhất."
        );
        setFormData({
          name: "",
          email: "",
          phone: "",
          subject: "",
          message: "",
        });
      } else {
        toast.error(result.error || "Có lỗi xảy ra khi gửi câu hỏi");
      }
    } catch (error) {
      console.error("Submit contact error:", error);
      toast.error("Có lỗi xảy ra khi gửi câu hỏi");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Vẫn Chưa Tìm Thấy{" "}
              <span className="bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
                Câu Trả Lời?
              </span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Đừng ngại liên hệ với chúng tôi! Đội ngũ hỗ trợ chuyên nghiệp sẵn
              sàng giải đáp mọi thắc mắc của bạn
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Methods */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h3 className="text-2xl font-bold text-gray-900 mb-8">
                Liên Hệ Trực Tiếp
              </h3>

              <div className="space-y-6">
                {contactMethods.map((method, index) => {
                  const Icon = method.icon;
                  return (
                    <motion.button
                      key={index}
                      onClick={method.action}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="w-full p-6 bg-gray-50 rounded-2xl border border-gray-100 hover:bg-white hover:shadow-lg transition-all duration-300 text-left group"
                    >
                      <div className="flex items-center gap-4">
                        <div
                          className={`w-14 h-14 rounded-full flex items-center justify-center transition-colors duration-300 ${
                            method.color === "green"
                              ? "bg-green-100 text-green-600 group-hover:bg-green-200"
                              : method.color === "blue"
                                ? "bg-blue-100 text-blue-600 group-hover:bg-blue-200"
                                : "bg-purple-100 text-purple-600 group-hover:bg-purple-200"
                          }`}
                        >
                          <Icon className="h-7 w-7" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900 mb-1">
                            {method.title}
                          </h4>
                          <p className="text-lg font-medium text-gray-800 mb-1">
                            {method.content}
                          </p>
                          <p className="text-sm text-gray-600">
                            {method.description}
                          </p>
                        </div>
                      </div>
                    </motion.button>
                  );
                })}
              </div>

              {/* Working Hours */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
                className="mt-8 p-6 bg-gradient-to-r from-pink-50 to-purple-50 rounded-2xl border border-pink-100"
              >
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center">
                    <Clock className="h-5 w-5 text-pink-600" />
                  </div>
                  <h4 className="font-semibold text-gray-900">Giờ Làm Việc</h4>
                </div>
                <div className="space-y-2 text-gray-700">
                  <p>• Thứ 2 - Thứ 6: 8:00 - 18:00</p>
                  <p>• Thứ 7 - Chủ nhật: 9:00 - 17:00</p>
                  <p>• Hotline hỗ trợ 24/7</p>
                </div>
              </motion.div>
            </motion.div>

            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h3 className="text-2xl font-bold text-gray-900 mb-8">
                Gửi Câu Hỏi Của Bạn
              </h3>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Họ và tên *
                    </label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <Input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        placeholder="Nhập họ và tên"
                        className="pl-10 py-3 rounded-xl border-gray-200 focus:border-pink-400 focus:ring-pink-400"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email *
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <Input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        placeholder="Nhập email"
                        className="pl-10 py-3 rounded-xl border-gray-200 focus:border-pink-400 focus:ring-pink-400"
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Số điện thoại
                  </label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <Input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      placeholder="Nhập số điện thoại"
                      className="pl-10 py-3 rounded-xl border-gray-200 focus:border-pink-400 focus:ring-pink-400"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Chủ đề *
                  </label>
                  <Input
                    type="text"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    required
                    placeholder="Nhập chủ đề câu hỏi"
                    className="py-3 rounded-xl border-gray-200 focus:border-pink-400 focus:ring-pink-400"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nội dung câu hỏi *
                  </label>
                  <div className="relative">
                    <MessageSquare className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <Textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={5}
                      placeholder="Mô tả chi tiết câu hỏi của bạn..."
                      className="pl-10 pt-3 rounded-xl border-gray-200 focus:border-pink-400 focus:ring-pink-400 resize-none"
                    />
                  </div>
                </div>

                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white py-3 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center gap-2"
                >
                  {isSubmitting ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Đang gửi...
                    </>
                  ) : (
                    <>
                      <Send className="h-5 w-5" />
                      Gửi Câu Hỏi
                    </>
                  )}
                </Button>
              </form>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
