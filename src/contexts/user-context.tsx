"use client";

import React, { createContext, useContext, ReactNode } from "react";
import {
  useUser,
  useUserAddresses,
  useUserWishlist,
  useUserPreferences,
} from "@/hooks";
import { User, Address } from "@/types";

interface UserContextType {
  // User state
  user: User | null;
  loading: boolean;
  error: string | null;

  // User actions
  updateProfile: (data: {
    name: string;
    phone?: string;
    dateOfBirth?: Date;
    gender?: "MALE" | "FEMALE" | "OTHER";
  }) => Promise<void>;
  uploadAvatar: (file: File) => Promise<void>;
  changePassword: (data: {
    currentPassword: string;
    newPassword: string;
  }) => Promise<void>;
  refetchUser: () => void;

  // Addresses
  addresses: Address[];
  addressesLoading: boolean;
  addressesError: string | null;
  addAddress: (data: {
    fullName: string;
    phone: string;
    address: string;
    ward: string;
    district: string;
    province: string;
    isDefault?: boolean;
  }) => Promise<void>;
  updateAddress: (id: string, data: Partial<Address>) => Promise<void>;
  deleteAddress: (id: string) => Promise<void>;
  setDefaultAddress: (id: string) => Promise<void>;
  refetchAddresses: () => void;

  // Wishlist
  wishlistItems: string[]; // Product IDs
  wishlistLoading: boolean;
  wishlistError: string | null;
  addToWishlist: (productId: string) => Promise<void>;
  removeFromWishlist: (productId: string) => Promise<void>;
  isInWishlist: (productId: string) => boolean;
  refetchWishlist: () => void;

  // Preferences
  preferences: {
    language: string;
    currency: string;
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
    };
    privacy: {
      showProfile: boolean;
      showOrders: boolean;
    };
  } | null;
  preferencesLoading: boolean;
  updatePreferences: (data: any) => Promise<void>;

  // Utilities
  isProfileComplete: boolean;
  profileCompletionPercentage: number;
  defaultAddress: Address | null;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
}

export function UserProvider({ children }: UserProviderProps) {
  // Use our custom hooks
  const {
    user,
    loading,
    error,
    updateProfile,
    uploadAvatar,
    changePassword,
    refetch: refetchUser,
  } = useUser();

  const {
    addresses,
    loading: addressesLoading,
    error: addressesError,
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress,
    refetch: refetchAddresses,
  } = useUserAddresses();

  const {
    wishlist,
    loading: wishlistLoading,
    error: wishlistError,
    addToWishlist,
    removeFromWishlist,
    refetch: refetchWishlist,
  } = useUserWishlist();

  const {
    preferences,
    loading: preferencesLoading,
    updatePreferences,
  } = useUserPreferences();

  // Convert wishlist to wishlistItems (Product IDs)
  const wishlistItems = wishlist?.map((product) => product.id) || [];

  // Utility functions
  const isInWishlist = (productId: string): boolean => {
    return wishlistItems.includes(productId);
  };

  const isProfileComplete = user
    ? !!(user.name && user.email && user.phone)
    : false;

  const profileCompletionPercentage = user
    ? (() => {
        const fields = [
          user.name,
          user.email,
          user.phone,
          user.dateOfBirth,
          user.gender,
          user.avatar,
        ];
        const completedFields = fields.filter((field) => !!field).length;
        return Math.round((completedFields / fields.length) * 100);
      })()
    : 0;

  const defaultAddress = addresses.find((addr) => addr.isDefault) || null;

  const value: UserContextType = {
    // User state
    user,
    loading,
    error,

    // User actions
    updateProfile,
    uploadAvatar,
    changePassword,
    refetchUser,

    // Addresses
    addresses,
    addressesLoading,
    addressesError,
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress,
    refetchAddresses,

    // Wishlist
    wishlistItems,
    wishlistLoading,
    wishlistError,
    addToWishlist,
    removeFromWishlist,
    isInWishlist,
    refetchWishlist,

    // Preferences
    preferences,
    preferencesLoading,
    updatePreferences,

    // Utilities
    isProfileComplete,
    profileCompletionPercentage,
    defaultAddress,
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
}

export function useUserContext() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUserContext must be used within a UserProvider");
  }
  return context;
}
