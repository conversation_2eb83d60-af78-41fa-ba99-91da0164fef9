// Main App Context Provider
export { AppContextProvider } from "./app-context";

// Individual Context Providers
export {
  EnhancedCartProvider,
  useEnhancedCart,
  useCartContext,
} from "./enhanced-cart-context";
export { UserProvider, useUserContext } from "./user-context";
export { SearchProvider, useSearchContext } from "./search-context";

// Legacy/Existing Contexts
export { useSettingsContext as useSettings } from "./SettingsContext";
export { useNotifications } from "./NotificationContext";
export { useAdminAuth } from "./AdminAuthContext";

// Legacy cart context (for backward compatibility)
export { CartProvider, useCart as useLegacyCart } from "./cart-context";
