"use client";

import React, { ReactNode } from "react";
import { EnhancedCartProvider } from "./enhanced-cart-context";
import { UserProvider } from "./user-context";
import { SearchProvider } from "./search-context";
import { SettingsProvider } from "./SettingsContext";
import { NotificationProvider } from "./NotificationContext";

interface AppContextProviderProps {
  children: ReactNode;
}

/**
 * Main App Context Provider that wraps all other context providers
 * This ensures proper order and dependency management between contexts
 */
export function AppContextProvider({ children }: AppContextProviderProps) {
  return (
    <SettingsProvider>
      <NotificationProvider>
        <UserProvider>
          <EnhancedCartProvider>
            <SearchProvider>{children}</SearchProvider>
          </EnhancedCartProvider>
        </UserProvider>
      </NotificationProvider>
    </SettingsProvider>
  );
}

// Re-export all context hooks for convenience
export { useEnhancedCart, useCartContext } from "./enhanced-cart-context";
export { useUserContext } from "./user-context";
export { useSearchContext } from "./search-context";
export { useSettingsContext } from "./SettingsContext";
export { useNotifications } from "./NotificationContext";
