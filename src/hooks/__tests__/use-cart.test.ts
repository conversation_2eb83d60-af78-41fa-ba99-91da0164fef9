import { renderHook, waitFor, act } from '@testing-library/react';
import { useCart, useQuickAddToCart, useCartValidation } from '../use-cart';
import { CartService } from '@/lib/services/cart-service';

// Mock the CartService
jest.mock('@/lib/services/cart-service');
const mockCartService = CartService as jest.Mocked<typeof CartService>;

// Mock data
const mockCartItem = {
  id: '1',
  productId: 'product-1',
  quantity: 2,
  product: {
    id: 'product-1',
    name: 'Test Product',
    slug: 'test-product',
    price: 100000,
    salePrice: 80000,
    images: ['image1.jpg'],
    stock: 10,
    category: { id: '1', name: 'Test Category', slug: 'test-category' },
  },
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

const mockCart = {
  id: 'cart-1',
  items: [mockCartItem],
  total: 160000,
  itemCount: 1,
  totalQuantity: 2,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

describe('useCart', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch cart successfully', async () => {
    mockCartService.getCart.mockResolvedValue(mockCart);

    const { result } = renderHook(() => useCart());

    expect(result.current.loading).toBe(true);
    expect(result.current.cart).toBe(null);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.cart).toEqual(mockCart);
    expect(result.current.error).toBe(null);
  });

  it('should handle empty cart', async () => {
    const emptyCart = { ...mockCart, items: [], total: 0, itemCount: 0, totalQuantity: 0 };
    mockCartService.getCart.mockResolvedValue(emptyCart);

    const { result } = renderHook(() => useCart());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.cart).toEqual(emptyCart);
    expect(result.current.cart?.items).toHaveLength(0);
  });

  it('should add item to cart', async () => {
    mockCartService.getCart.mockResolvedValue(mockCart);
    mockCartService.addToCart.mockResolvedValue(mockCart);

    const { result } = renderHook(() => useCart());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    await act(async () => {
      await result.current.addToCart({
        productId: 'product-2',
        quantity: 1,
      });
    });

    expect(mockCartService.addToCart).toHaveBeenCalledWith({
      productId: 'product-2',
      quantity: 1,
    });
  });

  it('should update cart item', async () => {
    mockCartService.getCart.mockResolvedValue(mockCart);
    mockCartService.updateCartItem.mockResolvedValue(mockCart);

    const { result } = renderHook(() => useCart());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    await act(async () => {
      await result.current.updateCartItem('1', { quantity: 3 });
    });

    expect(mockCartService.updateCartItem).toHaveBeenCalledWith('1', { quantity: 3 });
  });

  it('should remove item from cart', async () => {
    mockCartService.getCart.mockResolvedValue(mockCart);
    mockCartService.removeFromCart.mockResolvedValue(mockCart);

    const { result } = renderHook(() => useCart());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    await act(async () => {
      await result.current.removeFromCart('1');
    });

    expect(mockCartService.removeFromCart).toHaveBeenCalledWith('1');
  });

  it('should clear cart', async () => {
    mockCartService.getCart.mockResolvedValue(mockCart);
    mockCartService.clearCart.mockResolvedValue({ ...mockCart, items: [], total: 0 });

    const { result } = renderHook(() => useCart());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    await act(async () => {
      await result.current.clearCart();
    });

    expect(mockCartService.clearCart).toHaveBeenCalled();
  });

  it('should handle errors', async () => {
    const errorMessage = 'Failed to fetch cart';
    mockCartService.getCart.mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useCart());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toBe(errorMessage);
    expect(result.current.cart).toBe(null);
  });
});

describe('useQuickAddToCart', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should add product to cart quickly', async () => {
    mockCartService.addToCart.mockResolvedValue(mockCart);

    const { result } = renderHook(() => useQuickAddToCart());

    expect(result.current.loading).toBe(false);

    await act(async () => {
      await result.current.quickAdd('product-1', 2);
    });

    expect(mockCartService.addToCart).toHaveBeenCalledWith({
      productId: 'product-1',
      quantity: 2,
    });
  });

  it('should use default quantity of 1', async () => {
    mockCartService.addToCart.mockResolvedValue(mockCart);

    const { result } = renderHook(() => useQuickAddToCart());

    await act(async () => {
      await result.current.quickAdd('product-1');
    });

    expect(mockCartService.addToCart).toHaveBeenCalledWith({
      productId: 'product-1',
      quantity: 1,
    });
  });

  it('should handle errors', async () => {
    const errorMessage = 'Failed to add to cart';
    mockCartService.addToCart.mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useQuickAddToCart());

    await act(async () => {
      try {
        await result.current.quickAdd('product-1');
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe(errorMessage);
      }
    });
  });
});

describe('useCartValidation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should validate cart successfully', async () => {
    const validationResult = {
      isValid: true,
      invalidItems: [],
      errors: [],
    };
    mockCartService.validateCart.mockResolvedValue(validationResult);

    const { result } = renderHook(() => useCartValidation(mockCart));

    expect(result.current.loading).toBe(true);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.isValid).toBe(true);
    expect(result.current.invalidItems).toEqual([]);
    expect(result.current.errors).toEqual([]);
  });

  it('should handle invalid cart', async () => {
    const validationResult = {
      isValid: false,
      invalidItems: ['1'],
      errors: ['Product out of stock'],
    };
    mockCartService.validateCart.mockResolvedValue(validationResult);

    const { result } = renderHook(() => useCartValidation(mockCart));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.isValid).toBe(false);
    expect(result.current.invalidItems).toEqual(['1']);
    expect(result.current.errors).toEqual(['Product out of stock']);
  });

  it('should handle null cart', () => {
    const { result } = renderHook(() => useCartValidation(null));

    expect(result.current.loading).toBe(false);
    expect(result.current.isValid).toBe(true);
    expect(result.current.invalidItems).toEqual([]);
    expect(result.current.errors).toEqual([]);
  });

  it('should handle validation errors', async () => {
    const errorMessage = 'Validation failed';
    mockCartService.validateCart.mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useCartValidation(mockCart));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.isValid).toBe(false);
    expect(result.current.errors).toEqual([errorMessage]);
  });
});
