import { renderHook, waitFor, act } from "@testing-library/react";
import {
  useProducts,
  useProductBySlug,
  useFeaturedProducts,
} from "../use-products";
import { ProductService } from "@/lib/services/product-service";

// Mock the ProductService
jest.mock("@/lib/services/product-service", () => ({
  ProductService: {
    getProducts: jest.fn(),
    getProductBySlug: jest.fn(),
    getFeaturedProducts: jest.fn(),
  },
}));

const mockProductService = ProductService as jest.Mocked<typeof ProductService>;

// Mock data
const mockProducts = [
  {
    id: "1",
    name: "Test Product 1",
    slug: "test-product-1",
    price: 100000,
    salePrice: 80000,
    description: "Test description",
    images: ["image1.jpg"],
    stock: 10,
    category: { id: "1", name: "Test Category", slug: "test-category" },
    attributes: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "2",
    name: "Test Product 2",
    slug: "test-product-2",
    price: 200000,
    salePrice: null,
    description: "Test description 2",
    images: ["image2.jpg"],
    stock: 5,
    category: { id: "2", name: "Test Category 2", slug: "test-category-2" },
    attributes: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

const mockProductsResponse = {
  data: mockProducts,
  total: 2,
  page: 1,
  limit: 10,
  totalPages: 1,
};

describe("useProducts", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should fetch products successfully", async () => {
    mockProductService.getProducts.mockResolvedValue(mockProductsResponse);

    const { result } = renderHook(() => useProducts());

    expect(result.current.loading).toBe(true);
    expect(result.current.data).toEqual([]);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toEqual(mockProducts);
    expect(result.current.total).toBe(2);
    expect(result.current.totalPages).toBe(1);
    expect(result.current.error).toBe(null);
  });

  it("should handle search parameters", async () => {
    mockProductService.getProducts.mockResolvedValue(mockProductsResponse);

    const { result } = renderHook(() =>
      useProducts({ search: "test", category: "test-category" })
    );

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(mockProductService.getProducts).toHaveBeenCalledWith({
      search: "test",
      category: "test-category",
      page: 1,
      limit: 12,
    });
  });

  it("should handle pagination", async () => {
    mockProductService.getProducts.mockResolvedValue(mockProductsResponse);

    const { result } = renderHook(() => useProducts({ page: 2, limit: 20 }));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(mockProductService.getProducts).toHaveBeenCalledWith({
      page: 2,
      limit: 20,
    });
  });

  it("should handle errors", async () => {
    const errorMessage = "Failed to fetch products";
    mockProductService.getProducts.mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useProducts());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toBe(errorMessage);
    expect(result.current.data).toEqual([]);
  });

  it("should refetch data when refetch is called", async () => {
    mockProductService.getProducts.mockResolvedValue(mockProductsResponse);

    const { result } = renderHook(() => useProducts());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(mockProductService.getProducts).toHaveBeenCalledTimes(1);

    // Call refetch
    act(() => {
      result.current.refetch();
    });

    await waitFor(() => {
      expect(mockProductService.getProducts).toHaveBeenCalledTimes(2);
    });
  });
});

describe("useProductBySlug", () => {
  const mockProduct = mockProducts[0];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should fetch product by slug successfully", async () => {
    mockProductService.getProductBySlug.mockResolvedValue(mockProduct);

    const { result } = renderHook(() => useProductBySlug("test-product-1"));

    expect(result.current.loading).toBe(true);
    expect(result.current.product).toBe(null);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.product).toEqual(mockProduct);
    expect(result.current.error).toBe(null);
    expect(mockProductService.getProductBySlug).toHaveBeenCalledWith(
      "test-product-1"
    );
  });

  it("should handle null slug", () => {
    const { result } = renderHook(() => useProductBySlug(null));

    expect(result.current.loading).toBe(false);
    expect(result.current.product).toBe(null);
    expect(result.current.error).toBe(null);
    expect(mockProductService.getProductBySlug).not.toHaveBeenCalled();
  });

  it("should handle errors", async () => {
    const errorMessage = "Product not found";
    mockProductService.getProductBySlug.mockRejectedValue(
      new Error(errorMessage)
    );

    const { result } = renderHook(() => useProductBySlug("invalid-slug"));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toBe(errorMessage);
    expect(result.current.product).toBe(null);
  });
});

describe("useFeaturedProducts", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should fetch featured products successfully", async () => {
    mockProductService.getFeaturedProducts.mockResolvedValue(mockProducts);

    const { result } = renderHook(() => useFeaturedProducts());

    expect(result.current.loading).toBe(true);
    expect(result.current.products).toEqual([]);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.products).toEqual(mockProducts);
    expect(result.current.error).toBe(null);
  });

  it("should handle limit parameter", async () => {
    mockProductService.getFeaturedProducts.mockResolvedValue(mockProducts);

    const { result } = renderHook(() => useFeaturedProducts(5));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(mockProductService.getFeaturedProducts).toHaveBeenCalledWith(5);
  });

  it("should handle errors", async () => {
    const errorMessage = "Failed to fetch featured products";
    mockProductService.getFeaturedProducts.mockRejectedValue(
      new Error(errorMessage)
    );

    const { result } = renderHook(() => useFeaturedProducts());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toBe(errorMessage);
    expect(result.current.products).toEqual([]);
  });
});
