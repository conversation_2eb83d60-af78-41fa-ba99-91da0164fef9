"use client";

import { useState, useEffect, useCallback } from "react";
import { Order } from "@/types";
import { 
  OrderService, 
  OrderListParams, 
  CreateOrderData, 
  OrderStats,
  OrderTimeline 
} from "@/lib/services";

// Hook state interface
interface UseOrdersState {
  data: Order[];
  total: number;
  page: number;
  totalPages: number;
  loading: boolean;
  error: string | null;
}

// Main orders hook with pagination and filtering
export function useOrders(params: OrderListParams = {}) {
  const [state, setState] = useState<UseOrdersState>({
    data: [],
    total: 0,
    page: 1,
    totalPages: 0,
    loading: true,
    error: null,
  });

  const fetchOrders = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const response = await OrderService.getOrders(params);
      
      setState({
        data: response.data,
        total: response.total,
        page: response.page,
        totalPages: response.totalPages,
        loading: false,
        error: null,
      });
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : "Failed to fetch orders",
      }));
    }
  }, [JSON.stringify(params)]);

  useEffect(() => {
    fetchOrders();
  }, [fetchOrders]);

  const refetch = useCallback(() => {
    fetchOrders();
  }, [fetchOrders]);

  return {
    ...state,
    refetch,
  };
}

// Single order hook
export function useOrder(id: string | null) {
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchOrder = useCallback(async () => {
    if (!id) {
      setOrder(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await OrderService.getOrder(id);
      setOrder(response.data || null);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to fetch order");
      setOrder(null);
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchOrder();
  }, [fetchOrder]);

  const refetch = useCallback(() => {
    fetchOrder();
  }, [fetchOrder]);

  return {
    order,
    loading,
    error,
    refetch,
  };
}

// Order by tracking number hook
export function useOrderByTracking(trackingNumber: string | null) {
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchOrder = useCallback(async () => {
    if (!trackingNumber) {
      setOrder(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await OrderService.getOrderByTracking(trackingNumber);
      setOrder(response.data || null);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to fetch order");
      setOrder(null);
    } finally {
      setLoading(false);
    }
  }, [trackingNumber]);

  useEffect(() => {
    fetchOrder();
  }, [fetchOrder]);

  const refetch = useCallback(() => {
    fetchOrder();
  }, [fetchOrder]);

  return {
    order,
    loading,
    error,
    refetch,
  };
}

// Create order hook
export function useCreateOrder() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createOrder = useCallback(async (data: CreateOrderData) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await OrderService.createOrder(data);
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to create order";
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    createOrder,
    loading,
    error,
  };
}

// Order statistics hook
export function useOrderStats() {
  const [stats, setStats] = useState<OrderStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await OrderService.getOrderStats();
      setStats(response.data);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to fetch order stats");
      setStats(null);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  const refetch = useCallback(() => {
    fetchStats();
  }, [fetchStats]);

  return {
    stats,
    loading,
    error,
    refetch,
  };
}

// Order timeline hook
export function useOrderTimeline(order: Order | null) {
  const [timeline, setTimeline] = useState<OrderTimeline[]>([]);

  useEffect(() => {
    if (!order) {
      setTimeline([]);
      return;
    }

    const orderTimeline = OrderService.getOrderTimeline(order);
    setTimeline(orderTimeline);
  }, [order]);

  return timeline;
}

// Order status utilities hook
export function useOrderStatus(order: Order | null) {
  const statusLabel = order ? OrderService.getOrderStatusLabel(order.status) : "";
  const progress = order ? OrderService.getOrderProgress(order) : 0;
  const canCancel = order ? OrderService.canCancelOrder(order) : false;
  const canReturn = order ? OrderService.canReturnOrder(order) : false;

  return {
    statusLabel,
    progress,
    canCancel,
    canReturn,
  };
}

// Cancel order hook
export function useCancelOrder() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const cancelOrder = useCallback(async (orderId: string, reason?: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await OrderService.cancelOrder(orderId, reason);
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to cancel order";
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    cancelOrder,
    loading,
    error,
  };
}

// Return order hook
export function useReturnOrder() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const returnOrder = useCallback(async (orderId: string, reason?: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await OrderService.returnOrder(orderId, reason);
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to return order";
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    returnOrder,
    loading,
    error,
  };
}

// Recent orders hook
export function useRecentOrders(limit: number = 5) {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchRecentOrders = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await OrderService.getRecentOrders(limit);
      setOrders(response.data);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to fetch recent orders");
      setOrders([]);
    } finally {
      setLoading(false);
    }
  }, [limit]);

  useEffect(() => {
    fetchRecentOrders();
  }, [fetchRecentOrders]);

  const refetch = useCallback(() => {
    fetchRecentOrders();
  }, [fetchRecentOrders]);

  return {
    orders,
    loading,
    error,
    refetch,
  };
}
