import { useState } from "react";
import { toast } from "sonner";

export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  service?: string;
  subject: string;
  message: string;
}

export interface UseContactFormOptions {
  onSuccess?: (contactId: string) => void;
  onError?: (error: string) => void;
}

export function useContactForm(options: UseContactFormOptions = {}) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const submitForm = async (data: ContactFormData) => {
    setIsSubmitting(true);
    setIsSuccess(false);

    try {
      const response = await fetch("/api/contact", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Có lỗi xảy ra khi gửi tin nhắn");
      }

      setIsSuccess(true);
      toast.success(result.message || "G<PERSON>i tin nhắn thành công!");
      
      if (options.onSuccess && result.contactId) {
        options.onSuccess(result.contactId);
      }

      return { success: true, contactId: result.contactId };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra";
      toast.error(errorMessage);
      
      if (options.onError) {
        options.onError(errorMessage);
      }

      return { success: false, error: errorMessage };
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setIsSuccess(false);
  };

  return {
    submitForm,
    isSubmitting,
    isSuccess,
    resetForm,
  };
}
