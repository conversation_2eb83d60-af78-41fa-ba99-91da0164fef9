"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { Product, Category } from "@/types";
import { ProductService, CategoryService } from "@/lib/services";

// Search result interface
interface SearchResult {
  products: Product[];
  categories: Category[];
  total: number;
  query: string;
}

// Search state interface
interface UseSearchState {
  results: SearchResult;
  loading: boolean;
  error: string | null;
  hasSearched: boolean;
}

// Main search hook
export function useSearch() {
  const [state, setState] = useState<UseSearchState>({
    results: {
      products: [],
      categories: [],
      total: 0,
      query: "",
    },
    loading: false,
    error: null,
    hasSearched: false,
  });

  const search = useCallback(
    async (
      query: string,
      options?: {
        includeCategories?: boolean;
        limit?: number;
        categoryId?: string;
      }
    ) => {
      if (!query.trim()) {
        setState((prev) => ({
          ...prev,
          results: { products: [], categories: [], total: 0, query: "" },
          hasSearched: false,
        }));
        return;
      }

      try {
        setState((prev) => ({ ...prev, loading: true, error: null }));

        // Search products
        const productResponse = await ProductService.searchProducts({
          search: query,
          limit: options?.limit || 20,
          categoryId: options?.categoryId,
        });

        // Search categories if requested
        let categories: Category[] = [];
        if (options?.includeCategories) {
          const categoryResponse = await CategoryService.searchCategories({
            search: query,
            limit: 10,
          });
          categories = categoryResponse.data;
        }

        const results: SearchResult = {
          products: productResponse.data,
          categories,
          total: productResponse.total,
          query: query.trim(),
        };

        setState({
          results,
          loading: false,
          error: null,
          hasSearched: true,
        });

        // Add to search history
        addToSearchHistory(query.trim());
      } catch (error) {
        setState((prev) => ({
          ...prev,
          loading: false,
          error: error instanceof Error ? error.message : "Search failed",
        }));
      }
    },
    []
  );

  const clearSearch = useCallback(() => {
    setState({
      results: { products: [], categories: [], total: 0, query: "" },
      loading: false,
      error: null,
      hasSearched: false,
    });
  }, []);

  return {
    ...state,
    search,
    clearSearch,
  };
}

// Search suggestions hook
export function useSearchSuggestions(query: string, delay: number = 300) {
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const debouncedQuery = useMemo(() => {
    const handler = setTimeout(() => query, delay);
    return () => clearTimeout(handler);
  }, [query, delay]);

  useEffect(() => {
    const fetchSuggestions = async () => {
      if (!query.trim() || query.length < 2) {
        setSuggestions([]);
        return;
      }

      try {
        setLoading(true);
        const response = await ProductService.getSearchSuggestions(
          query.trim()
        );
        setSuggestions(response.data || []);
      } catch {
        setSuggestions([]);
      } finally {
        setLoading(false);
      }
    };

    const cleanup = debouncedQuery();
    fetchSuggestions();

    return cleanup;
  }, [debouncedQuery, query]);

  return {
    suggestions,
    loading,
  };
}

// Search history management
const SEARCH_HISTORY_KEY = "ns-shop-search-history";
const MAX_HISTORY_ITEMS = 10;

function getSearchHistory(): string[] {
  if (typeof window === "undefined") return [];

  try {
    const history = localStorage.getItem(SEARCH_HISTORY_KEY);
    return history ? JSON.parse(history) : [];
  } catch {
    return [];
  }
}

function addToSearchHistory(query: string) {
  if (typeof window === "undefined") return;

  try {
    const history = getSearchHistory();
    const filteredHistory = history.filter((item) => item !== query);
    const newHistory = [query, ...filteredHistory].slice(0, MAX_HISTORY_ITEMS);

    localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(newHistory));
  } catch {
    // Ignore localStorage errors
  }
}

function clearSearchHistory() {
  if (typeof window === "undefined") return;

  try {
    localStorage.removeItem(SEARCH_HISTORY_KEY);
  } catch {
    // Ignore localStorage errors
  }
}

// Search history hook
export function useSearchHistory() {
  const [history, setHistory] = useState<string[]>([]);

  useEffect(() => {
    setHistory(getSearchHistory());
  }, []);

  const addToHistory = useCallback((query: string) => {
    addToSearchHistory(query);
    setHistory(getSearchHistory());
  }, []);

  const clearHistory = useCallback(() => {
    clearSearchHistory();
    setHistory([]);
  }, []);

  const removeFromHistory = useCallback(
    (query: string) => {
      const newHistory = history.filter((item) => item !== query);
      try {
        localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(newHistory));
        setHistory(newHistory);
      } catch {
        // Ignore localStorage errors
      }
    },
    [history]
  );

  return {
    history,
    addToHistory,
    clearHistory,
    removeFromHistory,
  };
}

// Popular searches hook
export function usePopularSearches() {
  const [searches, setSearches] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPopularSearches = async () => {
      try {
        setLoading(true);
        const response = await ProductService.getPopularSearches();
        setSearches(response.data || []);
      } catch {
        setSearches([]);
      } finally {
        setLoading(false);
      }
    };

    fetchPopularSearches();
  }, []);

  return {
    searches,
    loading,
  };
}

// Advanced search hook with filters
export function useAdvancedSearch() {
  const [state, setState] = useState<UseSearchState>({
    results: {
      products: [],
      categories: [],
      total: 0,
      query: "",
    },
    loading: false,
    error: null,
    hasSearched: false,
  });

  const advancedSearch = useCallback(
    async (filters: {
      query?: string;
      categoryId?: string;
      minPrice?: number;
      maxPrice?: number;
      inStock?: boolean;
      featured?: boolean;
      sortBy?: string;
      sortOrder?: "asc" | "desc";
      page?: number;
      limit?: number;
    }) => {
      try {
        setState((prev) => ({ ...prev, loading: true, error: null }));

        const response = await ProductService.getProducts({
          search: filters.query,
          categoryId: filters.categoryId,
          minPrice: filters.minPrice,
          maxPrice: filters.maxPrice,
          inStock: filters.inStock,
          featured: filters.featured,
          sortBy: filters.sortBy,
          sortOrder: filters.sortOrder,
          page: filters.page || 1,
          limit: filters.limit || 20,
        });

        const results: SearchResult = {
          products: response.data,
          categories: [],
          total: response.total,
          query: filters.query || "",
        };

        setState({
          results,
          loading: false,
          error: null,
          hasSearched: true,
        });
      } catch (error) {
        setState((prev) => ({
          ...prev,
          loading: false,
          error:
            error instanceof Error ? error.message : "Advanced search failed",
        }));
      }
    },
    []
  );

  const clearSearch = useCallback(() => {
    setState({
      results: { products: [], categories: [], total: 0, query: "" },
      loading: false,
      error: null,
      hasSearched: false,
    });
  }, []);

  return {
    ...state,
    advancedSearch,
    clearSearch,
  };
}
