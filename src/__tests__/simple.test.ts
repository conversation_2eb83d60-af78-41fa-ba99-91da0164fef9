// Simple test to verify Jest setup
describe("Jest Setup", () => {
  it("should run basic tests", () => {
    expect(1 + 1).toBe(2);
  });

  it("should handle async operations", async () => {
    const promise = Promise.resolve("test");
    const result = await promise;
    expect(result).toBe("test");
  });

  it("should mock functions", () => {
    const mockFn = jest.fn();
    mockFn("test");
    expect(mockFn).toHaveBeenCalledWith("test");
  });
});

// Test utility functions
describe("Utility Functions", () => {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(price);
  };

  const calculateDiscount = (originalPrice: number, salePrice: number) => {
    return Math.round(((originalPrice - salePrice) / originalPrice) * 100);
  };

  it("should format price correctly", () => {
    const result1 = formatPrice(100000);
    const result2 = formatPrice(1500000);

    expect(result1).toContain("100");
    expect(result1).toContain("₫");
    expect(result2).toContain("1.500");
    expect(result2).toContain("₫");
  });

  it("should calculate discount percentage", () => {
    expect(calculateDiscount(100000, 80000)).toBe(20);
    expect(calculateDiscount(299000, 199000)).toBe(33);
  });

  it("should handle zero discount", () => {
    expect(calculateDiscount(100000, 100000)).toBe(0);
  });
});
