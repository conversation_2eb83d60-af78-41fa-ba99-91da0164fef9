"use client";

import { api } from "@/lib/api-client";
import { Order, OrderItem, Address, ApiResponse, PaginatedResponse } from "@/types";

export interface CreateOrderData {
  items: Array<{
    productId: string;
    quantity: number;
    price: number;
  }>;
  shippingAddress: Address;
  billingAddress?: Address;
  paymentMethod: "COD" | "BANK_TRANSFER" | "CREDIT_CARD";
  notes?: string;
  couponCode?: string;
}

export interface OrderFilters {
  status?: "PENDING" | "CONFIRMED" | "PROCESSING" | "SHIPPED" | "DELIVERED" | "CANCELLED";
  paymentStatus?: "PENDING" | "PAID" | "FAILED" | "REFUNDED";
  paymentMethod?: "COD" | "BANK_TRANSFER" | "CREDIT_CARD";
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}

export interface OrderListParams extends OrderFilters {
  page?: number;
  limit?: number;
  sort?: "createdAt" | "total" | "status";
  order?: "asc" | "desc";
}

export interface OrderStats {
  totalOrders: number;
  totalRevenue: number;
  pendingOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  averageOrderValue: number;
}

export class OrderService {
  private static readonly ENDPOINT = "/orders";

  // Get user orders
  static async getOrders(
    params: OrderListParams = {}
  ): Promise<PaginatedResponse<Order>> {
    return api.getList<Order>(this.ENDPOINT, params);
  }

  // Get single order
  static async getOrder(id: string): Promise<ApiResponse<Order>> {
    return api.getById<Order>(this.ENDPOINT, id);
  }

  // Create new order
  static async createOrder(data: CreateOrderData): Promise<ApiResponse<Order>> {
    return api.create<Order>(this.ENDPOINT, data);
  }

  // Cancel order
  static async cancelOrder(
    id: string,
    reason?: string
  ): Promise<ApiResponse<Order>> {
    return api.update<Order>(this.ENDPOINT, id, {
      status: "CANCELLED",
      cancelReason: reason,
    });
  }

  // Update order status (Admin)
  static async updateOrderStatus(
    id: string,
    status: Order["status"]
  ): Promise<ApiResponse<Order>> {
    return api.update<Order>(this.ENDPOINT, id, { status });
  }

  // Update payment status (Admin)
  static async updatePaymentStatus(
    id: string,
    paymentStatus: Order["paymentStatus"]
  ): Promise<ApiResponse<Order>> {
    return api.update<Order>(this.ENDPOINT, id, { paymentStatus });
  }

  // Get order tracking info
  static async getOrderTracking(
    id: string
  ): Promise<ApiResponse<any>> {
    return api.getById<any>(`${this.ENDPOINT}/${id}/tracking`, "");
  }

  // Request order return/refund
  static async requestReturn(
    id: string,
    items: Array<{ orderItemId: string; quantity: number; reason: string }>
  ): Promise<ApiResponse<void>> {
    return api.create<void>(`${this.ENDPOINT}/${id}/return`, { items });
  }

  // Get order statistics (Admin)
  static async getOrderStats(
    dateFrom?: string,
    dateTo?: string
  ): Promise<ApiResponse<OrderStats>> {
    return api.getById<OrderStats>(`${this.ENDPOINT}/stats`, "", {
      dateFrom,
      dateTo,
    });
  }

  // Utility methods
  static getOrderStatusLabel(status: Order["status"]): string {
    const labels = {
      PENDING: "Chờ xác nhận",
      CONFIRMED: "Đã xác nhận",
      PROCESSING: "Đang xử lý",
      SHIPPED: "Đang giao hàng",
      DELIVERED: "Đã giao hàng",
      CANCELLED: "Đã hủy",
    };
    return labels[status] || status;
  }

  static getPaymentStatusLabel(status: Order["paymentStatus"]): string {
    const labels = {
      PENDING: "Chờ thanh toán",
      PAID: "Đã thanh toán",
      FAILED: "Thanh toán thất bại",
      REFUNDED: "Đã hoàn tiền",
    };
    return labels[status] || status;
  }

  static getPaymentMethodLabel(method: Order["paymentMethod"]): string {
    const labels = {
      COD: "Thanh toán khi nhận hàng",
      BANK_TRANSFER: "Chuyển khoản ngân hàng",
      CREDIT_CARD: "Thẻ tín dụng",
    };
    return labels[method] || method;
  }

  static getOrderStatusColor(status: Order["status"]): string {
    const colors = {
      PENDING: "text-yellow-600 bg-yellow-50",
      CONFIRMED: "text-blue-600 bg-blue-50",
      PROCESSING: "text-purple-600 bg-purple-50",
      SHIPPED: "text-indigo-600 bg-indigo-50",
      DELIVERED: "text-green-600 bg-green-50",
      CANCELLED: "text-red-600 bg-red-50",
    };
    return colors[status] || "text-gray-600 bg-gray-50";
  }

  static getPaymentStatusColor(status: Order["paymentStatus"]): string {
    const colors = {
      PENDING: "text-yellow-600 bg-yellow-50",
      PAID: "text-green-600 bg-green-50",
      FAILED: "text-red-600 bg-red-50",
      REFUNDED: "text-gray-600 bg-gray-50",
    };
    return colors[status] || "text-gray-600 bg-gray-50";
  }

  static canCancelOrder(order: Order): boolean {
    return ["PENDING", "CONFIRMED"].includes(order.status);
  }

  static canRequestReturn(order: Order): boolean {
    return order.status === "DELIVERED" && order.paymentStatus === "PAID";
  }

  static isOrderPaid(order: Order): boolean {
    return order.paymentStatus === "PAID";
  }

  static isOrderCompleted(order: Order): boolean {
    return order.status === "DELIVERED";
  }

  static isOrderCancelled(order: Order): boolean {
    return order.status === "CANCELLED";
  }

  static calculateOrderTotal(items: OrderItem[]): number {
    return items.reduce((total, item) => total + item.total, 0);
  }

  static calculateOrderItemCount(items: OrderItem[]): number {
    return items.reduce((count, item) => count + item.quantity, 0);
  }

  static formatOrderNumber(order: Order): string {
    const date = new Date(order.createdAt);
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");
    const id = order.id.slice(-6).toUpperCase();
    
    return `NS${year}${month}${day}${id}`;
  }

  static getEstimatedDeliveryDate(order: Order): Date | null {
    if (order.status !== "SHIPPED") return null;
    
    // Estimate 2-3 days for delivery
    const shippedDate = new Date(order.updatedAt);
    const estimatedDate = new Date(shippedDate);
    estimatedDate.setDate(estimatedDate.getDate() + 3);
    
    return estimatedDate;
  }

  static getOrderProgress(status: Order["status"]): number {
    const progressMap = {
      PENDING: 20,
      CONFIRMED: 40,
      PROCESSING: 60,
      SHIPPED: 80,
      DELIVERED: 100,
      CANCELLED: 0,
    };
    return progressMap[status] || 0;
  }

  static getOrderTimeline(order: Order): Array<{
    status: string;
    label: string;
    date?: Date;
    completed: boolean;
  }> {
    const timeline = [
      { status: "PENDING", label: "Đặt hàng", completed: false },
      { status: "CONFIRMED", label: "Xác nhận", completed: false },
      { status: "PROCESSING", label: "Đang xử lý", completed: false },
      { status: "SHIPPED", label: "Đang giao hàng", completed: false },
      { status: "DELIVERED", label: "Đã giao hàng", completed: false },
    ];

    const statusOrder = ["PENDING", "CONFIRMED", "PROCESSING", "SHIPPED", "DELIVERED"];
    const currentIndex = statusOrder.indexOf(order.status);

    timeline.forEach((item, index) => {
      if (index <= currentIndex && order.status !== "CANCELLED") {
        item.completed = true;
        if (index === currentIndex) {
          item.date = new Date(order.updatedAt);
        }
      }
    });

    return timeline;
  }
}

export default OrderService;
