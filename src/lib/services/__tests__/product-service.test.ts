import { ProductService } from "../product-service";
import { api } from "@/lib/api-client";

// Mock the api client
jest.mock("@/lib/api-client", () => ({
  api: {
    getList: jest.fn(),
    getById: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  },
}));

const mockApi = api as jest.Mocked<typeof api>;

// Mock data
const mockProduct = {
  id: "1",
  name: "Test Product",
  slug: "test-product",
  price: 100000,
  salePrice: 80000,
  description: "Test description",
  images: ["image1.jpg"],
  stock: 10,
  category: { id: "1", name: "Test Category", slug: "test-category" },
  attributes: [],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

const mockProductsResponse = {
  data: [mockProduct],
  total: 1,
  page: 1,
  limit: 10,
  totalPages: 1,
};

describe("ProductService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("getProducts", () => {
    it("should fetch products successfully", async () => {
      mockApi.getList.mockResolvedValueOnce(mockProductsResponse);

      const result = await ProductService.getProducts();

      expect(mockApi.getList).toHaveBeenCalledWith("/products", {
        page: undefined,
        limit: undefined,
        search: undefined,
        sort: undefined,
        filter: {},
      });
      expect(result).toEqual(mockProductsResponse);
    });

    it("should handle search parameters", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockProductsResponse,
      } as Response);

      await ProductService.getProducts({
        search: "test",
        category: "test-category",
        page: 2,
        limit: 20,
      });

      expect(fetch).toHaveBeenCalledWith(
        "/api/products?page=2&limit=20&search=test&category=test-category"
      );
    });

    it("should handle API errors", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: async () => ({ error: "Server error" }),
      } as Response);

      await expect(ProductService.getProducts()).rejects.toThrow(
        "Server error"
      );
    });

    it("should handle network errors", async () => {
      mockFetch.mockRejectedValueOnce(new Error("Network error"));

      await expect(ProductService.getProducts()).rejects.toThrow(
        "Network error"
      );
    });
  });

  describe("getProductBySlug", () => {
    it("should fetch product by slug successfully", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockProduct,
      } as Response);

      const result = await ProductService.getProductBySlug("test-product");

      expect(fetch).toHaveBeenCalledWith("/api/products/test-product");
      expect(result).toEqual(mockProduct);
    });

    it("should handle product not found", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ error: "Product not found" }),
      } as Response);

      await expect(
        ProductService.getProductBySlug("invalid-slug")
      ).rejects.toThrow("Product not found");
    });
  });

  describe("getFeaturedProducts", () => {
    it("should fetch featured products successfully", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [mockProduct],
      } as Response);

      const result = await ProductService.getFeaturedProducts();

      expect(fetch).toHaveBeenCalledWith("/api/products/featured?limit=8");
      expect(result).toEqual([mockProduct]);
    });

    it("should handle custom limit", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [mockProduct],
      } as Response);

      await ProductService.getFeaturedProducts(5);

      expect(fetch).toHaveBeenCalledWith("/api/products/featured?limit=5");
    });
  });

  describe("getRelatedProducts", () => {
    it("should fetch related products successfully", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [mockProduct],
      } as Response);

      const result = await ProductService.getRelatedProducts("product-1");

      expect(fetch).toHaveBeenCalledWith(
        "/api/products/related?productId=product-1&limit=4"
      );
      expect(result).toEqual([mockProduct]);
    });

    it("should handle custom limit for related products", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [mockProduct],
      } as Response);

      await ProductService.getRelatedProducts("product-1", 6);

      expect(fetch).toHaveBeenCalledWith(
        "/api/products/related?productId=product-1&limit=6"
      );
    });
  });

  describe("searchProducts", () => {
    it("should search products successfully", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockProductsResponse,
      } as Response);

      const result = await ProductService.searchProducts("test query");

      expect(fetch).toHaveBeenCalledWith(
        "/api/products/search?q=test%20query&page=1&limit=12"
      );
      expect(result).toEqual(mockProductsResponse);
    });

    it("should handle search with filters", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockProductsResponse,
      } as Response);

      await ProductService.searchProducts("test", {
        category: "test-category",
        minPrice: 50000,
        maxPrice: 200000,
        page: 2,
        limit: 20,
      });

      expect(fetch).toHaveBeenCalledWith(
        "/api/products/search?q=test&page=2&limit=20&category=test-category&minPrice=50000&maxPrice=200000"
      );
    });
  });

  describe("URL building", () => {
    it("should build URLs with query parameters correctly", () => {
      // This is testing the internal URL building logic
      const params = new URLSearchParams({
        page: "1",
        limit: "12",
        search: "test product",
        category: "clothing",
      });

      expect(params.toString()).toBe(
        "page=1&limit=12&search=test+product&category=clothing"
      );
    });

    it("should handle empty parameters", () => {
      const params = new URLSearchParams();
      params.append("page", "1");
      params.append("limit", "12");

      expect(params.toString()).toBe("page=1&limit=12");
    });
  });
});
