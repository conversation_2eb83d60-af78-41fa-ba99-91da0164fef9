import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";
import { seedEmailTemplates } from "./email-templates-seed";

const prisma = new PrismaClient();

// ===== BASIC DATA SEEDING =====
async function seedBasicData() {
  console.log("🌱 Seeding basic data...");

  // Tạo admin user
  const adminPassword = await bcrypt.hash("admin123", 12);
  const admin = await prisma.adminUser.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      name: "NS Shop Admin",
      password: adminPassword,
      role: "ADMIN",
      isActive: true,
    },
  });

  // Tạo moderator user
  const moderatorPassword = await bcrypt.hash("moderator123", 12);
  const moderator = await prisma.adminUser.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      name: "NS Shop Moderator",
      password: moderatorPassword,
      role: "MODERAT<PERSON>",
      isActive: true,
      permissions: {
        manage_products: true,
        manage_orders: true,
        manage_categories: true,
        view_analytics: true,
        manage_users: true,
      },
      createdBy: admin.id,
    },
  });

  return { admin, moderator };
}

// ===== SETTINGS SEEDING =====
async function seedSettings() {
  console.log("🌱 Seeding settings...");

  const settings = [
    { key: "siteName", value: "NS Shop", type: "string" },
    {
      key: "siteDescription",
      value: "Cửa hàng thời trang trực tuyến hàng đầu Việt Nam",
      type: "string",
    },
    { key: "contactEmail", value: "<EMAIL>", type: "string" },
    { key: "contactPhone", value: "+84 ***********", type: "string" },
    { key: "address", value: "123 Đường ABC, Quận 1, TP.HCM", type: "string" },
    { key: "currency", value: "VND", type: "string" },
    { key: "shippingFee", value: 30000, type: "number" },
    { key: "freeShippingThreshold", value: 500000, type: "number" },
    { key: "taxRate", value: 0.1, type: "number" },
    { key: "maxOrderItems", value: 50, type: "number" },
  ];

  for (const setting of settings) {
    await prisma.setting.upsert({
      where: { key: setting.key },
      update: { value: setting.value },
      create: setting,
    });
  }
}

// ===== MEDIA SEEDING =====
async function seedMedia() {
  console.log("🌱 Seeding media...");

  const mediaItems = [];

  // Brand logos - sử dụng real brand logos
  const brandLogos = [
    {
      filename: "nike-logo.png",
      path: "brands/nike-logo.png",
      url: "https://logos-world.net/wp-content/uploads/2020/04/Nike-Logo.png",
      mimeType: "image/png",
      size: 15000,
      width: 200,
      height: 100,
      alt: "Nike Logo",
      title: "Nike Brand Logo",
      folder: "brands",
      type: "EXTERNAL",
      externalUrl:
        "https://logos-world.net/wp-content/uploads/2020/04/Nike-Logo.png",
    },
    {
      filename: "adidas-logo.png",
      path: "brands/adidas-logo.png",
      url: "https://logos-world.net/wp-content/uploads/2020/04/Adidas-Logo.png",
      mimeType: "image/png",
      size: 16000,
      width: 200,
      height: 100,
      alt: "Adidas Logo",
      title: "Adidas Brand Logo",
      folder: "brands",
      type: "EXTERNAL",
      externalUrl:
        "https://logos-world.net/wp-content/uploads/2020/04/Adidas-Logo.png",
    },
    {
      filename: "zara-logo.png",
      path: "brands/zara-logo.png",
      url: "https://logos-world.net/wp-content/uploads/2020/04/Zara-Logo.png",
      mimeType: "image/png",
      size: 14000,
      width: 200,
      height: 100,
      alt: "Zara Logo",
      title: "Zara Brand Logo",
      folder: "brands",
      type: "EXTERNAL",
      externalUrl:
        "https://logos-world.net/wp-content/uploads/2020/04/Zara-Logo.png",
    },
    {
      filename: "hm-logo.png",
      path: "brands/hm-logo.png",
      url: "https://logos-world.net/wp-content/uploads/2020/04/HM-Logo.png",
      mimeType: "image/png",
      size: 13000,
      width: 200,
      height: 100,
      alt: "H&M Logo",
      title: "H&M Brand Logo",
      folder: "brands",
      type: "EXTERNAL",
      externalUrl:
        "https://logos-world.net/wp-content/uploads/2020/04/HM-Logo.png",
    },
    {
      filename: "uniqlo-logo.png",
      path: "brands/uniqlo-logo.png",
      url: "https://logos-world.net/wp-content/uploads/2020/04/Uniqlo-Logo.png",
      mimeType: "image/png",
      size: 12000,
      width: 200,
      height: 100,
      alt: "Uniqlo Logo",
      title: "Uniqlo Brand Logo",
      folder: "brands",
      type: "EXTERNAL",
      externalUrl:
        "https://logos-world.net/wp-content/uploads/2020/04/Uniqlo-Logo.png",
    },
  ];

  // Category images - sử dụng fashion category images
  const categoryImages = [
    {
      filename: "ao-thun-category.jpg",
      path: "categories/ao-thun-category.jpg",
      url: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=300&fit=crop",
      mimeType: "image/jpeg",
      size: 45000,
      width: 400,
      height: 300,
      alt: "Áo thun category",
      title: "Danh mục áo thun",
      folder: "categories",
      type: "EXTERNAL",
      externalUrl:
        "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=300&fit=crop",
    },
    {
      filename: "vay-dam-category.jpg",
      path: "categories/vay-dam-category.jpg",
      url: "https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400&h=300&fit=crop",
      mimeType: "image/jpeg",
      size: 48000,
      width: 400,
      height: 300,
      alt: "Váy đầm category",
      title: "Danh mục váy đầm",
      folder: "categories",
      type: "EXTERNAL",
      externalUrl:
        "https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400&h=300&fit=crop",
    },
    // Thêm các category images khác...
  ];

  // Product images - sử dụng fashion product images
  const productImages = [];
  for (let i = 1; i <= 100; i++) {
    productImages.push({
      filename: `product-${i}.jpg`,
      path: `products/product-${i}.jpg`,
      url: `https://images.unsplash.com/photo-${1********0000 + i}?w=600&h=800&fit=crop`,
      mimeType: "image/jpeg",
      size: 85000 + Math.floor(Math.random() * 20000),
      width: 600,
      height: 800,
      alt: `Product ${i}`,
      title: `Product Image ${i}`,
      folder: "products",
      type: "EXTERNAL",
      externalUrl: `https://images.unsplash.com/photo-${1********0000 + i}?w=600&h=800&fit=crop`,
    });
  }

  // Combine all media
  const allMedia = [...brandLogos, ...categoryImages, ...productImages];

  // Create media records
  const createdMedia = [];
  for (const media of allMedia) {
    const created = await prisma.media.create({
      data: media,
    });
    createdMedia.push(created);
  }

  console.log(`✅ Media seeded successfully: ${createdMedia.length} items`);
  return createdMedia;
}

// ===== BRANDS SEEDING =====
async function seedBrands(mediaItems: any[]) {
  console.log("🌱 Seeding brands...");

  const brandLogos = mediaItems.filter((m) => m.folder === "brands");

  const brands = [
    {
      name: "Nike",
      slug: "nike",
      description:
        "Thương hiệu thể thao hàng đầu thế giới với slogan Just Do It",
      website: "https://www.nike.com",
      logoId: brandLogos[0]?.id,
      isActive: true,
    },
    {
      name: "Adidas",
      slug: "adidas",
      description: "Thương hiệu thể thao Đức nổi tiếng với 3 sọc đặc trưng",
      website: "https://www.adidas.com",
      logoId: brandLogos[1]?.id,
      isActive: true,
    },
    {
      name: "Zara",
      slug: "zara",
      description: "Thương hiệu thời trang nhanh hàng đầu từ Tây Ban Nha",
      website: "https://www.zara.com",
      logoId: brandLogos[2]?.id,
      isActive: true,
    },
    {
      name: "H&M",
      slug: "hm",
      description: "Thương hiệu thời trang bền vững từ Thụy Điển",
      website: "https://www.hm.com",
      logoId: brandLogos[3]?.id,
      isActive: true,
    },
    {
      name: "Uniqlo",
      slug: "uniqlo",
      description:
        "Thương hiệu thời trang minimalist chất lượng cao từ Nhật Bản",
      website: "https://www.uniqlo.com",
      logoId: brandLogos[4]?.id,
      isActive: true,
    },
  ];

  const createdBrands = [];
  for (const brand of brands) {
    const created = await prisma.brand.upsert({
      where: { slug: brand.slug },
      update: {},
      create: brand,
    });
    createdBrands.push(created);
  }

  console.log(`✅ Brands seeded successfully: ${createdBrands.length} items`);
  return createdBrands;
}

// ===== CATEGORIES SEEDING - Phù hợp với Homepage UI =====
async function seedCategories(mediaItems: any[]) {
  console.log("🌱 Seeding categories...");

  const categoryImages = mediaItems.filter((m) => m.folder === "categories");

  // Main categories theo homepage: "Áo thun", "Váy đầm", "Quần jeans", "Áo khoác", "Phụ kiện", "Giày dép"
  const categories = [
    {
      name: "Áo thun",
      slug: "ao-thun",
      description:
        "Áo thun nam nữ đa dạng kiểu dáng, chất liệu cotton thoáng mát",
      imageId: categoryImages[0]?.id,
      children: [
        {
          name: "Áo thun nam",
          slug: "ao-thun-nam",
          description: "Áo thun dành cho nam giới",
        },
        {
          name: "Áo thun nữ",
          slug: "ao-thun-nu",
          description: "Áo thun dành cho nữ giới",
        },
        {
          name: "Áo thun unisex",
          slug: "ao-thun-unisex",
          description: "Áo thun phù hợp cả nam và nữ",
        },
        {
          name: "Áo thun oversize",
          slug: "ao-thun-oversize",
          description: "Áo thun form rộng thời trang",
        },
      ],
    },
    {
      name: "Váy đầm",
      slug: "vay-dam",
      description: "Váy đầm nữ tính, thanh lịch cho mọi dịp",
      imageId: categoryImages[1]?.id,
      children: [
        {
          name: "Váy ngắn",
          slug: "vay-ngan",
          description: "Váy ngắn trẻ trung, năng động",
        },
        {
          name: "Váy dài",
          slug: "vay-dai",
          description: "Váy dài sang trọng, thanh lịch",
        },
        {
          name: "Đầm công sở",
          slug: "dam-cong-so",
          description: "Đầm phù hợp môi trường công sở",
        },
        {
          name: "Đầm dự tiệc",
          slug: "dam-du-tiec",
          description: "Đầm lộng lẫy cho các buổi tiệc",
        },
        {
          name: "Đầm maxi",
          slug: "dam-maxi",
          description: "Đầm dài thướt tha, nữ tính",
        },
      ],
    },
    {
      name: "Quần jeans",
      slug: "quan-jeans",
      description: "Quần jeans nam nữ đa dạng kiểu dáng, chất liệu bền đẹp",
      children: [
        {
          name: "Quần jeans nam",
          slug: "quan-jeans-nam",
          description: "Quần jeans dành cho nam giới",
        },
        {
          name: "Quần jeans nữ",
          slug: "quan-jeans-nu",
          description: "Quần jeans dành cho nữ giới",
        },
        {
          name: "Quần jeans skinny",
          slug: "quan-jeans-skinny",
          description: "Quần jeans ôm sát, tôn dáng",
        },
        {
          name: "Quần jeans boyfriend",
          slug: "quan-jeans-boyfriend",
          description: "Quần jeans form rộng thoải mái",
        },
        {
          name: "Quần jeans rách",
          slug: "quan-jeans-rach",
          description: "Quần jeans phong cách cá tính",
        },
      ],
    },
    {
      name: "Áo khoác",
      slug: "ao-khoac",
      description: "Áo khoác thời trang cho mọi mùa trong năm",
      children: [
        {
          name: "Áo khoác nam",
          slug: "ao-khoac-nam",
          description: "Áo khoác dành cho nam giới",
        },
        {
          name: "Áo khoác nữ",
          slug: "ao-khoac-nu",
          description: "Áo khoác dành cho nữ giới",
        },
        {
          name: "Áo hoodie",
          slug: "ao-hoodie",
          description: "Áo hoodie trẻ trung, năng động",
        },
        {
          name: "Áo blazer",
          slug: "ao-blazer",
          description: "Áo blazer lịch sự, chuyên nghiệp",
        },
        {
          name: "Áo bomber",
          slug: "ao-bomber",
          description: "Áo bomber phong cách thể thao",
        },
        {
          name: "Áo len",
          slug: "ao-len",
          description: "Áo len ấm áp cho mùa đông",
        },
      ],
    },
    {
      name: "Phụ kiện",
      slug: "phu-kien",
      description: "Phụ kiện thời trang hoàn thiện phong cách",
      children: [
        {
          name: "Túi xách",
          slug: "tui-xach",
          description: "Túi xách đa dạng kiểu dáng",
        },
        { name: "Ví", slug: "vi", description: "Ví nam nữ chất lượng cao" },
        {
          name: "Thắt lưng",
          slug: "that-lung",
          description: "Thắt lưng da và vải",
        },
        { name: "Mũ nón", slug: "mu-non", description: "Mũ nón thời trang" },
        {
          name: "Kính mát",
          slug: "kinh-mat",
          description: "Kính mát bảo vệ mắt",
        },
        { name: "Đồng hồ", slug: "dong-ho", description: "Đồng hồ thời trang" },
        {
          name: "Trang sức",
          slug: "trang-suc",
          description: "Trang sức và phụ kiện",
        },
      ],
    },
    {
      name: "Giày dép",
      slug: "giay-dep",
      description: "Giày dép thời trang cho mọi hoạt động",
      children: [
        {
          name: "Giày sneaker",
          slug: "giay-sneaker",
          description: "Giày sneaker thể thao",
        },
        {
          name: "Giày cao gót",
          slug: "giay-cao-got",
          description: "Giày cao gót nữ tính",
        },
        {
          name: "Giày boot",
          slug: "giay-boot",
          description: "Giày boot cá tính",
        },
        {
          name: "Giày sandal",
          slug: "giay-sandal",
          description: "Giày sandal thoáng mát",
        },
        { name: "Giày tây", slug: "giay-tay", description: "Giày tây lịch sự" },
        { name: "Dép", slug: "dep", description: "Dép thoải mái hàng ngày" },
      ],
    },
  ];

  const createdCategories = [];

  for (const category of categories) {
    // Tạo parent category
    const parentCategory = await prisma.category.upsert({
      where: { slug: category.slug },
      update: {},
      create: {
        name: category.name,
        slug: category.slug,
        description: category.description,
        imageId: category.imageId,
      },
    });
    createdCategories.push(parentCategory);

    // Tạo subcategories
    for (const child of category.children) {
      const childCategory = await prisma.category.upsert({
        where: { slug: child.slug },
        update: {},
        create: {
          name: child.name,
          slug: child.slug,
          description: child.description,
          parentId: parentCategory.id,
        },
      });
      createdCategories.push(childCategory);
    }
  }

  console.log(
    `✅ Categories seeded successfully: ${createdCategories.length} items`
  );
  return createdCategories;
}

// ===== ATTRIBUTES SEEDING =====
async function seedAttributes() {
  console.log("🌱 Seeding attributes...");

  const attributes = [
    {
      name: "Kích thước",
      slug: "kich-thuoc",
      type: "SELECT",
      values: ["XS", "S", "M", "L", "XL", "XXL", "XXXL"],
    },
    {
      name: "Màu sắc",
      slug: "mau-sac",
      type: "COLOR",
      values: [
        "Đen",
        "Trắng",
        "Xám",
        "Hồng",
        "Xanh dương",
        "Xanh lá",
        "Đỏ",
        "Vàng",
        "Nâu",
        "Tím",
      ],
    },
    {
      name: "Chất liệu",
      slug: "chat-lieu",
      type: "SELECT",
      values: [
        "Cotton",
        "Polyester",
        "Linen",
        "Silk",
        "Wool",
        "Denim",
        "Leather",
        "Synthetic",
      ],
    },
    {
      name: "Phong cách",
      slug: "phong-cach",
      type: "SELECT",
      values: [
        "Casual",
        "Formal",
        "Sport",
        "Vintage",
        "Modern",
        "Classic",
        "Trendy",
        "Minimalist",
      ],
    },
    {
      name: "Giới tính",
      slug: "gioi-tinh",
      type: "SELECT",
      values: ["Nam", "Nữ", "Unisex"],
    },
  ];

  const createdAttributes = [];
  for (const attr of attributes) {
    const attribute = await prisma.attribute.upsert({
      where: { slug: attr.slug },
      update: {},
      create: {
        name: attr.name,
        slug: attr.slug,
        type: attr.type,
      },
    });

    // Tạo attribute values
    for (const value of attr.values) {
      const slug = value
        .toLowerCase()
        .replace(/\s+/g, "-")
        .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
        .replace(/[èéẹẻẽêềếệểễ]/g, "e")
        .replace(/[ìíịỉĩ]/g, "i")
        .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
        .replace(/[ùúụủũưừứựửữ]/g, "u")
        .replace(/[ỳýỵỷỹ]/g, "y")
        .replace(/[đ]/g, "d")
        .replace(/[^a-z0-9-]/g, "");

      await prisma.attributeValue.upsert({
        where: {
          attributeId_slug: {
            attributeId: attribute.id,
            slug: slug,
          },
        },
        update: {},
        create: {
          attributeId: attribute.id,
          value: value,
          slug: slug,
        },
      });
    }

    createdAttributes.push(attribute);
  }

  console.log(
    `✅ Attributes seeded successfully: ${createdAttributes.length} items`
  );
  return createdAttributes;
}

// ===== USERS SEEDING =====
async function seedUsers() {
  console.log("🌱 Seeding users...");

  const users = [];

  // Tạo 20 user accounts với dữ liệu realistic hơn
  const vietnameseNames = [
    "Nguyễn Văn An",
    "Trần Thị Bình",
    "Lê Hoàng Cường",
    "Phạm Thị Dung",
    "Hoàng Văn Em",
    "Vũ Thị Phương",
    "Đặng Minh Giang",
    "Bùi Thị Hoa",
    "Ngô Văn Inh",
    "Lý Thị Kim",
    "Đinh Văn Long",
    "Tạ Thị Mai",
    "Dương Văn Nam",
    "Chu Thị Oanh",
    "Võ Văn Phúc",
    "Đỗ Thị Quỳnh",
    "Phan Văn Rồng",
    "Lưu Thị Sương",
    "Trịnh Văn Tài",
    "Mai Thị Uyên",
  ];

  const phoneNumbers = [
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
  ];

  for (let i = 1; i <= 20; i++) {
    const userPassword = await bcrypt.hash(`user${i}123`, 12);
    const user = await prisma.user.upsert({
      where: { email: `user${i}@nsshop.com` },
      update: {},
      create: {
        email: `user${i}@nsshop.com`,
        name: vietnameseNames[i - 1],
        password: userPassword,
        phone: phoneNumbers[i - 1],
        gender: i % 3 === 0 ? "OTHER" : i % 2 === 0 ? "FEMALE" : "MALE",
        dateOfBirth: new Date(
          1980 + Math.floor(Math.random() * 25),
          Math.floor(Math.random() * 12),
          Math.floor(Math.random() * 28) + 1
        ),
      },
    });
    users.push(user);

    // Tạo địa chỉ cho user
    await prisma.address.create({
      data: {
        userId: user.id,
        fullName: user.name,
        phone: user.phone || "",
        address: `${Math.floor(Math.random() * 999) + 1} Đường ${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}`,
        ward: `Phường ${Math.floor(Math.random() * 20) + 1}`,
        district: `Quận ${Math.floor(Math.random() * 12) + 1}`,
        province: "TP. Hồ Chí Minh",
        isDefault: true,
      },
    });

    // Tạo cart cho user
    await prisma.cart.create({
      data: {
        userId: user.id,
      },
    });
  }

  console.log(`✅ Users seeded successfully: ${users.length} items`);
  return users;
}

// ===== PRODUCTS SEEDING - Realistic Vietnamese Fashion Products =====
async function seedProducts(
  mediaItems: any[],
  categories: any[],
  brands: any[]
) {
  console.log("🌱 Seeding products...");

  if (categories.length === 0 || brands.length === 0) {
    throw new Error("Categories and brands must be seeded first.");
  }

  // Lấy product images từ media
  const productImages = mediaItems.filter((m) => m.folder === "products");

  // Realistic Vietnamese fashion products
  const productTemplates = {
    "ao-thun": [
      {
        name: "Áo Thun Cotton Basic",
        price: [150000, 300000],
        description: "Áo thun cotton 100% thoáng mát, form regular fit",
      },
      {
        name: "Áo Thun Polo Nam",
        price: [200000, 400000],
        description: "Áo polo nam lịch sự, phù hợp đi làm và dạo phố",
      },
      {
        name: "Áo Thun Oversize Unisex",
        price: [180000, 350000],
        description: "Áo thun form rộng thời trang, phong cách streetwear",
      },
      {
        name: "Áo Thun Crop Top Nữ",
        price: [120000, 250000],
        description: "Áo thun crop top nữ tính, phù hợp mix đồ",
      },
      {
        name: "Áo Thun Graphic Tee",
        price: [160000, 320000],
        description: "Áo thun in hình độc đáo, thể hiện cá tính",
      },
    ],
    "vay-dam": [
      {
        name: "Váy Midi Xòe",
        price: [300000, 600000],
        description: "Váy midi xòe nữ tính, phù hợp dự tiệc",
      },
      {
        name: "Đầm Maxi Bohemian",
        price: [400000, 800000],
        description: "Đầm maxi phong cách boho, thoải mái và thanh lịch",
      },
      {
        name: "Váy Ngắn Denim",
        price: [250000, 500000],
        description: "Váy ngắn chất liệu denim, phong cách trẻ trung",
      },
      {
        name: "Đầm Công Sở Thanh Lịch",
        price: [350000, 700000],
        description: "Đầm công sở lịch sự, phù hợp môi trường làm việc",
      },
      {
        name: "Váy Hoa Vintage",
        price: [280000, 550000],
        description: "Váy hoa phong cách vintage, nữ tính và duyên dáng",
      },
    ],
    "quan-jeans": [
      {
        name: "Quần Jeans Skinny",
        price: [350000, 700000],
        description: "Quần jeans skinny ôm sát, tôn dáng hoàn hảo",
      },
      {
        name: "Quần Jeans Boyfriend",
        price: [380000, 750000],
        description: "Quần jeans boyfriend form rộng thoải mái",
      },
      {
        name: "Quần Jeans Rách Gối",
        price: [320000, 650000],
        description: "Quần jeans rách gối phong cách cá tính",
      },
      {
        name: "Quần Jeans Ống Loe",
        price: [400000, 800000],
        description: "Quần jeans ống loe retro, thời trang và độc đáo",
      },
      {
        name: "Quần Jeans Đen Basic",
        price: [300000, 600000],
        description: "Quần jeans đen basic, dễ phối đồ",
      },
    ],
    "ao-khoac": [
      {
        name: "Áo Hoodie Zip",
        price: [400000, 800000],
        description: "Áo hoodie có khóa kéo, ấm áp và thời trang",
      },
      {
        name: "Áo Blazer Nữ",
        price: [500000, 1000000],
        description: "Áo blazer nữ lịch sự, phù hợp công sở",
      },
      {
        name: "Áo Bomber Jacket",
        price: [450000, 900000],
        description: "Áo bomber jacket phong cách thể thao",
      },
      {
        name: "Áo Cardigan Len",
        price: [350000, 700000],
        description: "Áo cardigan len mềm mại, ấm áp mùa đông",
      },
      {
        name: "Áo Khoác Denim",
        price: [380000, 750000],
        description: "Áo khoác denim classic, không bao giờ lỗi mốt",
      },
    ],
    "phu-kien": [
      {
        name: "Túi Xách Tote",
        price: [200000, 500000],
        description: "Túi xách tote da PU cao cấp, tiện dụng",
      },
      {
        name: "Ví Da Nam",
        price: [150000, 400000],
        description: "Ví da nam sang trọng, nhiều ngăn tiện lợi",
      },
      {
        name: "Thắt Lưng Da",
        price: [180000, 450000],
        description: "Thắt lưng da thật, khóa kim loại bền đẹp",
      },
      {
        name: "Mũ Bucket Hat",
        price: [100000, 250000],
        description: "Mũ bucket hat thời trang, chống nắng hiệu quả",
      },
      {
        name: "Kính Mát Aviator",
        price: [250000, 600000],
        description: "Kính mát aviator classic, bảo vệ mắt tối ưu",
      },
    ],
    "giay-dep": [
      {
        name: "Giày Sneaker Canvas",
        price: [300000, 700000],
        description: "Giày sneaker canvas thoáng khí, phong cách casual",
      },
      {
        name: "Giày Cao Gót 7cm",
        price: [400000, 900000],
        description: "Giày cao gót 7cm thanh lịch, tôn dáng",
      },
      {
        name: "Giày Boot Chelsea",
        price: [500000, 1200000],
        description: "Giày boot Chelsea da thật, phong cách cá tính",
      },
      {
        name: "Sandal Đế Xuồng",
        price: [250000, 550000],
        description: "Sandal đế xuồng thoải mái, phù hợp mùa hè",
      },
      {
        name: "Dép Slide Thể Thao",
        price: [150000, 350000],
        description: "Dép slide thể thao êm ái, tiện lợi",
      },
    ],
  };

  const createdProducts = [];
  let productIndex = 1;

  // Lấy subcategories (không có parentId null)
  const subcategories = categories.filter((cat) => cat.parentId !== null);

  for (const category of subcategories) {
    const parentCategory = categories.find(
      (cat) => cat.id === category.parentId
    );
    if (!parentCategory) continue;

    const templates = productTemplates[parentCategory.slug] || [];
    if (templates.length === 0) continue;

    // Tạo 8-12 sản phẩm cho mỗi subcategory
    const productCount = Math.floor(Math.random() * 5) + 8;

    for (let i = 0; i < productCount; i++) {
      const template = templates[i % templates.length];
      const brand = brands[Math.floor(Math.random() * brands.length)];

      // Tạo variations cho tên sản phẩm
      const colors = [
        "Đen",
        "Trắng",
        "Xám",
        "Hồng",
        "Xanh",
        "Đỏ",
        "Vàng",
        "Nâu",
      ];
      const styles = ["Basic", "Premium", "Limited", "Classic", "Trendy"];

      const color = colors[Math.floor(Math.random() * colors.length)];
      const style = styles[Math.floor(Math.random() * styles.length)];

      const productName = `${template.name} ${style} ${color}`;
      const slug =
        productName
          .toLowerCase()
          .replace(/\s+/g, "-")
          .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
          .replace(/[èéẹẻẽêềếệểễ]/g, "e")
          .replace(/[ìíịỉĩ]/g, "i")
          .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
          .replace(/[ùúụủũưừứựửữ]/g, "u")
          .replace(/[ỳýỵỷỹ]/g, "y")
          .replace(/[đ]/g, "d")
          .replace(/[^a-z0-9-]/g, "") + `-${productIndex}`;

      const price = Math.floor(
        Math.random() * (template.price[1] - template.price[0]) +
          template.price[0]
      );
      const salePrice =
        Math.random() > 0.7
          ? Math.floor(price * (0.7 + Math.random() * 0.2))
          : null;
      const stock = Math.floor(Math.random() * 100) + 20;

      try {
        const product = await prisma.product.create({
          data: {
            name: productName,
            slug: slug,
            description: `${template.description}. Sản phẩm chất lượng cao từ thương hiệu ${brand.name}.`,
            price: price,
            salePrice: salePrice,
            sku: `NS${String(productIndex).padStart(4, "0")}`,
            stock: stock,
            categoryId: category.id,
            brandId: brand.id,
            featured: productIndex <= 20, // 20 sản phẩm đầu là featured
            tags: [
              style.toLowerCase(),
              color.toLowerCase(),
              parentCategory.slug,
            ],
            avgRating: Math.random() * 2 + 3, // Rating từ 3-5
            reviewCount: Math.floor(Math.random() * 50),
          },
        });

        // Tạo ProductMedia relationships
        const numImages = Math.floor(Math.random() * 3) + 2; // 2-4 images per product
        for (let j = 0; j < numImages; j++) {
          const mediaIndex =
            ((productIndex - 1) * 4 + j) % productImages.length;
          const media = productImages[mediaIndex];

          if (media) {
            await prisma.productMedia.create({
              data: {
                productId: product.id,
                mediaId: media.id,
                order: j,
                isPrimary: j === 0, // First image is primary
              },
            });
          }
        }

        createdProducts.push(product);
        productIndex++;
      } catch (error) {
        console.error(`Error creating product ${productName}:`, error);
      }
    }
  }

  console.log(
    `✅ Products seeded successfully: ${createdProducts.length} items`
  );
  return createdProducts;
}

// ===== INVENTORY SEEDING =====
async function seedInventory() {
  console.log("🌱 Seeding inventory entries...");

  // Lấy tất cả products
  const products = await prisma.product.findMany();

  if (products.length === 0) {
    console.log("⚠️ No products found. Skipping inventory seeding.");
    return;
  }

  const locations = [
    "Kho Hà Nội - Kệ A1",
    "Kho Hà Nội - Kệ A2",
    "Kho Hà Nội - Kệ B1",
    "Kho TP.HCM - Kệ C1",
    "Kho TP.HCM - Kệ C2",
    "Kho Đà Nẵng - Kệ D1",
  ];

  for (const product of products) {
    // Random quantity từ 50-200
    const quantity = Math.floor(Math.random() * 150) + 50;
    // Random reserved từ 0-20
    const reserved = Math.floor(Math.random() * 21);
    const available = quantity - reserved;
    const location = locations[Math.floor(Math.random() * locations.length)];

    const inventoryEntry = await prisma.inventoryEntry.upsert({
      where: { productId: product.id },
      update: {},
      create: {
        productId: product.id,
        quantity,
        available,
        reserved,
        minStock: Math.floor(Math.random() * 10) + 5, // Random minStock 5-15
        maxStock: quantity + Math.floor(Math.random() * 50) + 50, // maxStock higher than current quantity
        location,
      },
    });

    // Tạo stock movement cho việc nhập kho ban đầu
    await prisma.stockMovement.create({
      data: {
        inventoryEntryId: inventoryEntry.id,
        type: "IN",
        quantity,
        reason: "Nhập kho ban đầu",
        reference: `INIT-${product.sku}`,
        notes: `Nhập kho ban đầu ${quantity} sản phẩm ${product.name}`,
      },
    });

    // Random tạo thêm một số stock movements
    const movementCount = Math.floor(Math.random() * 3) + 1; // 1-3 movements
    for (let i = 0; i < movementCount; i++) {
      const movementTypes = ["IN", "OUT", "ADJUSTMENT"];
      const type = movementTypes[
        Math.floor(Math.random() * movementTypes.length)
      ] as "IN" | "OUT" | "ADJUSTMENT";
      const movementQuantity = Math.floor(Math.random() * 20) + 1;

      let reason = "";
      let reference = "";

      switch (type) {
        case "IN":
          reason = "Nhập hàng bổ sung";
          reference = `RESTOCK-${Date.now()}-${i}`;
          break;
        case "OUT":
          reason = "Xuất hàng bán lẻ";
          reference = `SALE-${Date.now()}-${i}`;
          break;
        case "ADJUSTMENT":
          reason = "Điều chỉnh tồn kho";
          reference = `ADJ-${Date.now()}-${i}`;
          break;
      }

      await prisma.stockMovement.create({
        data: {
          inventoryEntryId: inventoryEntry.id,
          type,
          quantity: type === "OUT" ? -movementQuantity : movementQuantity,
          reason,
          reference,
          notes: `${reason} - ${movementQuantity} sản phẩm`,
        },
      });
    }
  }

  console.log(
    `✅ Inventory seeded successfully for ${products.length} products`
  );
}

// ===== PRODUCT ATTRIBUTES SEEDING =====
async function seedProductAttributes() {
  console.log("🌱 Seeding product attributes...");

  // Lấy tất cả products và attributes
  const products = await prisma.product.findMany();
  const attributes = await prisma.attribute.findMany({
    include: { values: true },
  });

  if (products.length === 0 || attributes.length === 0) {
    console.log(
      "⚠️ No products or attributes found. Skipping product attributes seeding."
    );
    return;
  }

  let createdCount = 0;

  for (const product of products) {
    // Mỗi product sẽ có 2-4 attributes random
    const numAttributes = Math.floor(Math.random() * 3) + 2;
    const selectedAttributes = attributes
      .sort(() => 0.5 - Math.random())
      .slice(0, numAttributes);

    for (const attribute of selectedAttributes) {
      // Random chọn 1 value từ attribute
      const randomValue =
        attribute.values[Math.floor(Math.random() * attribute.values.length)];

      try {
        await prisma.productAttribute.create({
          data: {
            productId: product.id,
            attributeId: attribute.id,
            attributeValueId: randomValue.id,
          },
        });
        createdCount++;
      } catch (error) {
        // Skip nếu đã tồn tại (có thể có unique constraint)
        console.log(`Skipping duplicate product attribute for ${product.name}`);
      }
    }
  }

  console.log(
    `✅ Product attributes seeded successfully: ${createdCount} items`
  );
}

// ===== CART ITEMS SEEDING =====
async function seedCartItems() {
  console.log("🌱 Seeding cart items...");

  // Lấy tất cả carts và products
  const carts = await prisma.cart.findMany();
  const products = await prisma.product.findMany();

  if (carts.length === 0 || products.length === 0) {
    console.log("⚠️ No carts or products found. Skipping cart items seeding.");
    return;
  }

  let createdCount = 0;

  for (const cart of carts) {
    // Mỗi cart sẽ có 1-5 items
    const numItems = Math.floor(Math.random() * 5) + 1;
    const selectedProducts = products
      .sort(() => 0.5 - Math.random())
      .slice(0, numItems);

    for (const product of selectedProducts) {
      const quantity = Math.floor(Math.random() * 3) + 1; // 1-3 quantity

      try {
        await prisma.cartItem.create({
          data: {
            cartId: cart.id,
            productId: product.id,
            quantity: quantity,
            price: product.salePrice || product.price,
          },
        });
        createdCount++;
      } catch (error) {
        console.log(`Error creating cart item: ${error}`);
      }
    }
  }

  console.log(`✅ Cart items seeded successfully: ${createdCount} items`);
}

// ===== WISHLIST ITEMS SEEDING =====
async function seedWishlistItems() {
  console.log("🌱 Seeding wishlist items...");

  // Lấy tất cả users và products
  const users = await prisma.user.findMany();
  const products = await prisma.product.findMany();

  if (users.length === 0 || products.length === 0) {
    console.log(
      "⚠️ No users or products found. Skipping wishlist items seeding."
    );
    return;
  }

  let createdCount = 0;

  for (const user of users) {
    // Mỗi user sẽ có 2-8 wishlist items
    const numItems = Math.floor(Math.random() * 7) + 2;
    const selectedProducts = products
      .sort(() => 0.5 - Math.random())
      .slice(0, numItems);

    for (const product of selectedProducts) {
      try {
        await prisma.wishlistItem.create({
          data: {
            userId: user.id,
            productId: product.id,
          },
        });
        createdCount++;
      } catch (error) {
        // Skip nếu đã tồn tại
        console.log(`Skipping duplicate wishlist item for user ${user.email}`);
      }
    }
  }

  console.log(`✅ Wishlist items seeded successfully: ${createdCount} items`);
}

// ===== ORDERS SEEDING =====
async function seedOrders() {
  console.log("🌱 Seeding orders...");

  // Lấy tất cả users và products
  const users = await prisma.user.findMany();
  const products = await prisma.product.findMany();

  if (users.length === 0 || products.length === 0) {
    console.log("⚠️ No users or products found. Skipping orders seeding.");
    return;
  }

  const orderStatuses = [
    "PENDING",
    "CONFIRMED",
    "PROCESSING",
    "SHIPPED",
    "DELIVERED",
    "CANCELLED",
  ];
  let createdOrdersCount = 0;
  let createdOrderItemsCount = 0;

  for (const user of users) {
    // Mỗi user sẽ có 1-4 orders
    const numOrders = Math.floor(Math.random() * 4) + 1;

    for (let i = 0; i < numOrders; i++) {
      const status = orderStatuses[
        Math.floor(Math.random() * orderStatuses.length)
      ] as any;

      // Tạo order
      const order = await prisma.order.create({
        data: {
          userId: user.id,
          status: status,
          total: 0, // Sẽ cập nhật sau
          notes: `Đơn hàng ${i + 1} của ${user.name}`,
          shippingAddress: {
            fullName: user.name,
            phone: `09${Math.floor(Math.random() * *********)
              .toString()
              .padStart(8, "0")}`,
            address: `${Math.floor(Math.random() * 999) + 1} Đường ABC`,
            ward: `Phường ${Math.floor(Math.random() * 20) + 1}`,
            district: `Quận ${Math.floor(Math.random() * 12) + 1}`,
            province: "TP. Hồ Chí Minh",
          },
        },
      });

      // Tạo order items
      const numItems = Math.floor(Math.random() * 4) + 1; // 1-4 items per order
      const selectedProducts = products
        .sort(() => 0.5 - Math.random())
        .slice(0, numItems);

      let subtotal = 0;

      for (const product of selectedProducts) {
        const quantity = Math.floor(Math.random() * 3) + 1; // 1-3 quantity
        const price = product.salePrice || product.price;
        const itemTotal = price * quantity;
        subtotal += itemTotal;

        await prisma.orderItem.create({
          data: {
            orderId: order.id,
            productId: product.id,
            quantity: quantity,
            price: price,
            total: itemTotal,
          },
        });
        createdOrderItemsCount++;
      }

      // Cập nhật order total
      const shippingFee = Math.floor(Math.random() * 50000) + 20000; // 20k-70k
      const total = subtotal + shippingFee;

      await prisma.order.update({
        where: { id: order.id },
        data: {
          total: total,
        },
      });

      createdOrdersCount++;
    }
  }

  console.log(
    `✅ Orders seeded successfully: ${createdOrdersCount} orders, ${createdOrderItemsCount} order items`
  );
}

// ===== REVIEWS SEEDING =====
async function seedReviews() {
  console.log("🌱 Seeding reviews...");

  // Lấy tất cả users và products
  const users = await prisma.user.findMany();
  const products = await prisma.product.findMany();

  if (users.length === 0 || products.length === 0) {
    console.log("⚠️ No users or products found. Skipping reviews seeding.");
    return;
  }

  const reviewTexts = [
    "Sản phẩm rất tốt, chất lượng như mô tả. Tôi rất hài lòng!",
    "Giao hàng nhanh, đóng gói cẩn thận. Sẽ mua lại lần sau.",
    "Chất liệu tốt, form dáng đẹp. Đúng như hình ảnh.",
    "Giá cả hợp lý, chất lượng ổn. Recommend cho mọi người.",
    "Sản phẩm đẹp, nhưng size hơi nhỏ so với mô tả.",
    "Màu sắc đẹp, chất liệu mềm mại. Rất thích!",
    "Đóng gói kỹ càng, sản phẩm không bị hỏng. Tuyệt vời!",
    "Chất lượng tốt với mức giá này. Sẽ giới thiệu cho bạn bè.",
  ];

  let createdCount = 0;

  // Tạo reviews cho 60% products (random)
  const reviewableProducts = products
    .sort(() => 0.5 - Math.random())
    .slice(0, Math.floor(products.length * 0.6));

  for (const product of reviewableProducts) {
    // Mỗi product sẽ có 1-5 reviews
    const numReviews = Math.floor(Math.random() * 5) + 1;
    const reviewers = users
      .sort(() => 0.5 - Math.random())
      .slice(0, numReviews);

    for (const user of reviewers) {
      const rating = Math.floor(Math.random() * 2) + 4; // 4-5 stars (mostly positive)
      const reviewText =
        reviewTexts[Math.floor(Math.random() * reviewTexts.length)];

      try {
        await prisma.review.create({
          data: {
            userId: user.id,
            productId: product.id,
            rating: rating,
            comment: reviewText,
            status: Math.random() > 0.8 ? "PENDING" : "APPROVED", // 80% approved reviews
          },
        });
        createdCount++;
      } catch (error) {
        // Skip nếu user đã review product này rồi
        console.log(`Skipping duplicate review for product ${product.name}`);
      }
    }
  }

  console.log(`✅ Reviews seeded successfully: ${createdCount} items`);
}

// ===== MENU & MENU ITEMS SEEDING =====
async function seedMenus() {
  console.log("🌱 Seeding menus and menu items...");

  // Tạo main navigation menu
  const mainMenu = await prisma.menu.create({
    data: {
      name: "Main Navigation",
      location: "header",
      description: "Main website navigation menu",
      isActive: true,
    },
  });

  // Lấy categories để tạo menu items
  const categories = await prisma.category.findMany({
    where: { parentId: null }, // Chỉ lấy main categories
  });

  let menuItemsCount = 0;

  // Tạo menu items cho categories
  for (let i = 0; i < categories.length; i++) {
    const category = categories[i];
    await prisma.menuItem.create({
      data: {
        menuId: mainMenu.id,
        title: category.name,
        url: `/categories/${category.slug}`,
        order: i + 1,
        isActive: true,
      },
    });
    menuItemsCount++;
  }

  // Thêm các menu items khác
  const additionalItems = [
    { title: "Trang chủ", url: "/", order: 0 },
    { title: "Giới thiệu", url: "/about", order: categories.length + 1 },
    { title: "Liên hệ", url: "/contact", order: categories.length + 2 },
    { title: "Blog", url: "/blog", order: categories.length + 3 },
  ];

  for (const item of additionalItems) {
    await prisma.menuItem.create({
      data: {
        menuId: mainMenu.id,
        title: item.title,
        url: item.url,
        order: item.order,
        isActive: true,
      },
    });
    menuItemsCount++;
  }

  // Tạo footer menu
  const footerMenu = await prisma.menu.create({
    data: {
      name: "Footer Links",
      location: "footer",
      description: "Footer navigation links",
      isActive: true,
    },
  });

  const footerItems = [
    { title: "Chính sách bảo mật", url: "/privacy-policy", order: 1 },
    { title: "Điều khoản sử dụng", url: "/terms-of-service", order: 2 },
    { title: "Chính sách đổi trả", url: "/return-policy", order: 3 },
    { title: "Hướng dẫn mua hàng", url: "/shopping-guide", order: 4 },
    { title: "Câu hỏi thường gặp", url: "/faq", order: 5 },
  ];

  for (const item of footerItems) {
    await prisma.menuItem.create({
      data: {
        menuId: footerMenu.id,
        title: item.title,
        url: item.url,
        order: item.order,
        isActive: true,
      },
    });
    menuItemsCount++;
  }

  console.log(
    `✅ Menus seeded successfully: 2 menus, ${menuItemsCount} menu items`
  );
}

// ===== PAGES SEEDING =====
async function seedPages() {
  console.log("🌱 Seeding static pages...");

  // Lấy admin user để làm author
  const admin = await prisma.adminUser.findFirst({
    where: { email: "<EMAIL>" },
  });

  if (!admin) {
    console.log("⚠️ No admin user found. Skipping pages seeding.");
    return;
  }

  const pages = [
    {
      title: "Giới thiệu",
      slug: "about",
      content: `
        <h1>Về NS Shop</h1>
        <p>NS Shop là cửa hàng thời trang trực tuyến hàng đầu Việt Nam, chuyên cung cấp các sản phẩm thời trang chất lượng cao với giá cả hợp lý.</p>

        <h2>Sứ mệnh</h2>
        <p>Chúng tôi cam kết mang đến cho khách hàng những sản phẩm thời trang đẹp, chất lượng và phù hợp với xu hướng hiện đại.</p>

        <h2>Tầm nhìn</h2>
        <p>Trở thành thương hiệu thời trang được yêu thích nhất tại Việt Nam, góp phần nâng cao phong cách thời trang của người Việt.</p>
      `,
      metaTitle: "Giới thiệu về NS Shop - Thời trang chất lượng",
      metaDescription:
        "Tìm hiểu về NS Shop, cửa hàng thời trang trực tuyến hàng đầu Việt Nam với sản phẩm chất lượng và giá cả hợp lý.",
    },
    {
      title: "Liên hệ",
      slug: "contact",
      content: `
        <h1>Liên hệ với chúng tôi</h1>

        <h2>Thông tin liên hệ</h2>
        <p><strong>Địa chỉ:</strong> 123 Đường Nguyễn Huệ, Quận 1, TP. Hồ Chí Minh</p>
        <p><strong>Điện thoại:</strong> 0901 234 567</p>
        <p><strong>Email:</strong> <EMAIL></p>

        <h2>Giờ làm việc</h2>
        <p>Thứ 2 - Thứ 6: 8:00 - 18:00</p>
        <p>Thứ 7 - Chủ nhật: 9:00 - 17:00</p>

        <h2>Hỗ trợ khách hàng</h2>
        <p>Chúng tôi luôn sẵn sàng hỗ trợ bạn 24/7 qua hotline và email.</p>
      `,
      metaTitle: "Liên hệ NS Shop - Hỗ trợ khách hàng 24/7",
      metaDescription:
        "Liên hệ với NS Shop để được hỗ trợ tư vấn và mua sắm. Hotline: 0901 234 567, Email: <EMAIL>",
    },
    {
      title: "Chính sách bảo mật",
      slug: "privacy-policy",
      content: `
        <h1>Chính sách bảo mật</h1>

        <h2>Thu thập thông tin</h2>
        <p>Chúng tôi chỉ thu thập thông tin cần thiết để cung cấp dịch vụ tốt nhất cho khách hàng.</p>

        <h2>Sử dụng thông tin</h2>
        <p>Thông tin khách hàng được sử dụng để xử lý đơn hàng, giao hàng và hỗ trợ khách hàng.</p>

        <h2>Bảo mật thông tin</h2>
        <p>Chúng tôi cam kết bảo mật tuyệt đối thông tin cá nhân của khách hàng.</p>
      `,
      metaTitle: "Chính sách bảo mật - NS Shop",
      metaDescription:
        "Tìm hiểu về chính sách bảo mật thông tin khách hàng tại NS Shop.",
    },
    {
      title: "Điều khoản sử dụng",
      slug: "terms-of-service",
      content: `
        <h1>Điều khoản sử dụng</h1>

        <h2>Quy định chung</h2>
        <p>Khi sử dụng website NS Shop, bạn đồng ý tuân thủ các điều khoản và điều kiện sau.</p>

        <h2>Quyền và nghĩa vụ</h2>
        <p>Khách hàng có quyền được hỗ trợ và nghĩa vụ cung cấp thông tin chính xác.</p>

        <h2>Thanh toán</h2>
        <p>Chúng tôi chấp nhận nhiều hình thức thanh toán an toàn và tiện lợi.</p>
      `,
      metaTitle: "Điều khoản sử dụng - NS Shop",
      metaDescription: "Điều khoản và điều kiện sử dụng website NS Shop.",
    },
    {
      title: "Chính sách đổi trả",
      slug: "return-policy",
      content: `
        <h1>Chính sách đổi trả</h1>

        <h2>Điều kiện đổi trả</h2>
        <p>Sản phẩm có thể đổi trả trong vòng 7 ngày kể từ ngày nhận hàng.</p>

        <h2>Quy trình đổi trả</h2>
        <p>1. Liên hệ hotline để thông báo đổi trả</p>
        <p>2. Đóng gói sản phẩm theo yêu cầu</p>
        <p>3. Gửi hàng về địa chỉ của chúng tôi</p>

        <h2>Chi phí đổi trả</h2>
        <p>Miễn phí đổi trả nếu lỗi từ phía shop.</p>
      `,
      metaTitle: "Chính sách đổi trả - NS Shop",
      metaDescription:
        "Tìm hiểu về chính sách đổi trả sản phẩm tại NS Shop. Đổi trả miễn phí trong 7 ngày.",
    },
  ];

  let createdCount = 0;

  for (const page of pages) {
    await prisma.page.create({
      data: {
        ...page,
        status: "PUBLISHED" as any,
        authorId: admin.id,
      },
    });
    createdCount++;
  }

  console.log(`✅ Pages seeded successfully: ${createdCount} items`);
}

// ===== POSTS SEEDING =====
async function seedPosts() {
  console.log("🌱 Seeding blog posts...");

  // Lấy admin user để làm author
  const admin = await prisma.adminUser.findFirst({
    where: { email: "<EMAIL>" },
  });

  if (!admin) {
    console.log("⚠️ No admin user found. Skipping posts seeding.");
    return;
  }

  const posts = [
    {
      title: "Xu hướng thời trang Thu Đông 2024",
      slug: "xu-huong-thoi-trang-thu-dong-2024",
      content: `
        <h1>Xu hướng thời trang Thu Đông 2024</h1>

        <p>Mùa Thu Đông 2024 đang đến gần, và đây là thời điểm hoàn hảo để cập nhật tủ đồ của bạn với những xu hướng thời trang mới nhất.</p>

        <h2>1. Màu sắc chủ đạo</h2>
        <p>Năm nay, các tông màu ấm áp như nâu đất, cam đỏ và xanh rêu sẽ thống trị thế giới thời trang.</p>

        <h2>2. Chất liệu nổi bật</h2>
        <p>Len cashmere, da lộn và vải tweed sẽ là những chất liệu được ưa chuộng nhất.</p>

        <h2>3. Phong cách layering</h2>
        <p>Nghệ thuật phối đồ nhiều lớp sẽ giúp bạn vừa ấm áp vừa thời trang.</p>
      `,
      excerpt:
        "Khám phá những xu hướng thời trang Thu Đông 2024 với màu sắc ấm áp và phong cách layering độc đáo.",
      featured: true,
      tags: ["thời trang", "xu hướng", "thu đông", "2024"],
    },
    {
      title: "Cách phối đồ công sở thanh lịch",
      slug: "cach-phoi-do-cong-so-thanh-lich",
      content: `
        <h1>Cách phối đồ công sở thanh lịch</h1>

        <p>Trang phục công sở không chỉ cần chuyên nghiệp mà còn phải thể hiện phong cách cá nhân của bạn.</p>

        <h2>Nguyên tắc cơ bản</h2>
        <p>Luôn chọn những trang phục vừa vặn, màu sắc trang nhã và chất liệu tốt.</p>

        <h2>Phụ kiện quan trọng</h2>
        <p>Túi xách, giày và đồng hồ sẽ hoàn thiện set đồ công sở của bạn.</p>
      `,
      excerpt:
        "Hướng dẫn chi tiết cách phối đồ công sở thanh lịch và chuyên nghiệp cho phái đẹp.",
      featured: false,
      tags: ["công sở", "phối đồ", "thanh lịch", "chuyên nghiệp"],
    },
    {
      title: "Bí quyết chọn size quần áo chuẩn",
      slug: "bi-quyet-chon-size-quan-ao-chuan",
      content: `
        <h1>Bí quyết chọn size quần áo chuẩn</h1>

        <p>Việc chọn đúng size quần áo là rất quan trọng để bạn luôn tự tin và thoải mái.</p>

        <h2>Cách đo số đo cơ thể</h2>
        <p>Sử dụng thước dây để đo vòng ngực, vòng eo và vòng mông một cách chính xác.</p>

        <h2>Bảng size chuẩn</h2>
        <p>Tham khảo bảng size của từng thương hiệu để chọn được size phù hợp nhất.</p>
      `,
      excerpt:
        "Hướng dẫn cách đo và chọn size quần áo chuẩn để luôn vừa vặn và thoải mái.",
      featured: false,
      tags: ["size guide", "đo size", "quần áo", "hướng dẫn"],
    },
  ];

  let createdCount = 0;

  for (const post of posts) {
    await prisma.post.create({
      data: {
        ...post,
        status: "PUBLISHED" as any,
        authorId: admin.id,
      },
    });
    createdCount++;
  }

  console.log(`✅ Posts seeded successfully: ${createdCount} items`);
}

// ===== PROMOTIONS SEEDING =====
async function seedPromotions() {
  console.log("🌱 Seeding promotions...");

  // Lấy admin user để làm createdBy
  const admin = await prisma.adminUser.findFirst({
    where: { email: "<EMAIL>" },
  });

  if (!admin) {
    console.log("⚠️ No admin user found. Skipping promotions seeding.");
    return;
  }

  const promotions = [
    {
      name: "Giảm giá 20% toàn bộ sản phẩm",
      code: "SALE20",
      description: "Áp dụng cho tất cả sản phẩm trong cửa hàng",
      type: "PERCENTAGE",
      value: 20,
      minOrderAmount: 500000, // 500k
      maxDiscountAmount: 200000, // 200k
      usageLimit: 1000,
      usageCount: 0,
      startDate: new Date("2024-01-01"),
      endDate: new Date("2024-12-31"),
      isActive: true,
      applicableProducts: [],
      applicableCategories: [],
      excludedProducts: [],
      excludedCategories: [],
      createdBy: admin.id,
    },
    {
      name: "Miễn phí vận chuyển",
      code: "FREESHIP",
      description: "Miễn phí vận chuyển cho đơn hàng từ 300k",
      type: "FIXED_AMOUNT",
      value: 30000, // 30k shipping fee
      minOrderAmount: 300000, // 300k
      maxDiscountAmount: 30000,
      usageLimit: 500,
      usageCount: 0,
      startDate: new Date("2024-01-01"),
      endDate: new Date("2024-12-31"),
      isActive: true,
      applicableProducts: [],
      applicableCategories: [],
      excludedProducts: [],
      excludedCategories: [],
      createdBy: admin.id,
    },
    {
      name: "Khách hàng mới giảm 15%",
      code: "NEWCUSTOMER15",
      description: "Dành riêng cho khách hàng mới đăng ký",
      type: "PERCENTAGE",
      value: 15,
      minOrderAmount: 200000, // 200k
      maxDiscountAmount: 150000, // 150k
      usageLimit: 100,
      usageCount: 0,
      startDate: new Date("2024-01-01"),
      endDate: new Date("2024-12-31"),
      isActive: true,
      applicableProducts: [],
      applicableCategories: [],
      excludedProducts: [],
      excludedCategories: [],
      createdBy: admin.id,
    },
    {
      name: "Flash Sale cuối tuần",
      code: "WEEKEND50",
      description: "Giảm 50k cho đơn hàng cuối tuần",
      type: "FIXED_AMOUNT",
      value: 50000,
      minOrderAmount: 400000, // 400k
      maxDiscountAmount: 50000,
      usageLimit: 200,
      usageCount: 0,
      startDate: new Date("2024-01-01"),
      endDate: new Date("2024-12-31"),
      isActive: true,
      applicableProducts: [],
      applicableCategories: [],
      excludedProducts: [],
      excludedCategories: [],
      createdBy: admin.id,
    },
  ];

  let createdCount = 0;

  for (const promotion of promotions) {
    await prisma.promotion.upsert({
      where: { code: promotion.code },
      update: {
        ...promotion,
        type: promotion.type as any,
      },
      create: {
        ...promotion,
        type: promotion.type as any,
      },
    });
    createdCount++;
  }

  console.log(`✅ Promotions seeded successfully: ${createdCount} items`);
}

// ===== PAYMENT GATEWAYS SEEDING =====
async function seedPaymentGateways() {
  console.log("🌱 Seeding payment gateways...");

  const paymentGateways = [
    {
      name: "COD - Thanh toán khi nhận hàng",
      provider: "COD",
      isActive: true,
      isDefault: true,
      config: {
        supportedRegions: ["all"],
        maxAmount: ********, // 10 triệu
        processingTime: "0 days",
      },
      credentials: {},
      supportedMethods: ["cash"],
      fees: { fixed: 0, percentage: 0 },
      limits: { min: 0, max: ******** },
    },
    {
      name: "VNPay",
      provider: "VNPAY",
      isActive: true,
      isDefault: false,
      config: {
        apiUrl: "https://sandbox.vnpayment.vn",
        supportedCards: ["ATM", "VISA", "MASTERCARD"],
      },
      credentials: {
        merchantId: "demo_merchant",
        secretKey: "demo_secret",
      },
      supportedMethods: ["card", "bank_transfer"],
      fees: { fixed: 0, percentage: 2.5 },
      limits: { min: 10000, max: ******** },
    },
    {
      name: "MoMo",
      provider: "MOMO",
      isActive: true,
      isDefault: false,
      config: {
        apiUrl: "https://test-payment.momo.vn",
      },
      credentials: {
        partnerCode: "demo_partner",
        accessKey: "demo_access",
        secretKey: "demo_secret",
      },
      supportedMethods: ["wallet"],
      fees: { fixed: 0, percentage: 2.0 },
      limits: { min: 10000, max: ******** },
    },
    {
      name: "ZaloPay",
      provider: "ZALOPAY",
      isActive: true,
      isDefault: false,
      config: {
        apiUrl: "https://sb-openapi.zalopay.vn",
      },
      credentials: {
        appId: "demo_app",
        key1: "demo_key1",
        key2: "demo_key2",
      },
      supportedMethods: ["wallet"],
      fees: { fixed: 0, percentage: 1.8 },
      limits: { min: 10000, max: ******** },
    },
    {
      name: "Chuyển khoản ngân hàng",
      provider: "BANK_TRANSFER",
      isActive: true,
      isDefault: false,
      config: {
        processingTime: "1-2 business days",
      },
      credentials: {
        bankName: "Vietcombank",
        accountNumber: "**********",
        accountName: "CONG TY NS SHOP",
        branchName: "Chi nhánh Hà Nội",
      },
      supportedMethods: ["bank_transfer"],
      fees: { fixed: 0, percentage: 0 },
      limits: { min: 50000, max: ********* },
    },
  ];

  let createdCount = 0;

  for (const gateway of paymentGateways) {
    await prisma.paymentGateway.create({
      data: {
        ...gateway,
        provider: gateway.provider as any,
      },
    });
    createdCount++;
  }

  console.log(`✅ Payment gateways seeded successfully: ${createdCount} items`);
}

// ===== SHIPPING ZONES SEEDING =====
async function seedShippingZones() {
  console.log("🌱 Seeding shipping zones...");

  const shippingZones = [
    {
      name: "Nội thành Hà Nội",
      description: "Các quận nội thành Hà Nội",
      provinces: [
        "Hoàn Kiếm",
        "Ba Đình",
        "Đống Đa",
        "Hai Bà Trưng",
        "Cầu Giấy",
        "Tây Hồ",
      ],
      isActive: true,
    },
    {
      name: "Ngoại thành Hà Nội",
      description: "Các huyện ngoại thành Hà Nội",
      provinces: ["Gia Lâm", "Đông Anh", "Sóc Sơn", "Mê Linh", "Thanh Trì"],
      isActive: true,
    },
    {
      name: "TP. Hồ Chí Minh",
      description: "Thành phố Hồ Chí Minh và các quận lân cận",
      provinces: [
        "Quận 1",
        "Quận 2",
        "Quận 3",
        "Quận 4",
        "Quận 5",
        "Quận 7",
        "Thủ Đức",
      ],
      isActive: true,
    },
    {
      name: "Miền Bắc",
      description: "Các tỉnh thành miền Bắc (trừ Hà Nội)",
      provinces: [
        "Hải Phòng",
        "Quảng Ninh",
        "Thái Nguyên",
        "Vĩnh Phúc",
        "Bắc Ninh",
      ],
      isActive: true,
    },
    {
      name: "Miền Trung",
      description: "Các tỉnh thành miền Trung",
      provinces: ["Đà Nẵng", "Huế", "Quảng Nam", "Quảng Ngãi", "Nha Trang"],
      isActive: true,
    },
    {
      name: "Miền Nam",
      description: "Các tỉnh thành miền Nam (trừ TP.HCM)",
      provinces: ["Bình Dương", "Đồng Nai", "Vũng Tàu", "Cần Thơ", "An Giang"],
      isActive: true,
    },
  ];

  let createdCount = 0;

  for (const zone of shippingZones) {
    await prisma.shippingZone.create({
      data: zone,
    });
    createdCount++;
  }

  console.log(`✅ Shipping zones seeded successfully: ${createdCount} items`);
  return createdCount;
}

// ===== SHIPPING METHODS SEEDING =====
async function seedShippingMethods() {
  console.log("🌱 Seeding shipping methods...");

  // Lấy shipping zones để tạo methods
  const zones = await prisma.shippingZone.findMany();

  if (zones.length === 0) {
    console.log(
      "⚠️ No shipping zones found. Skipping shipping methods seeding."
    );
    return;
  }

  const shippingMethods = [
    {
      name: "Giao hàng tiêu chuẩn",
      description: "Giao hàng trong 3-5 ngày làm việc",
      type: "STANDARD",
      baseFee: 30000,
      estimatedDays: "3-5 ngày",
      freeShippingMin: 500000, // Free ship từ 500k
      isActive: true,
    },
    {
      name: "Giao hàng nhanh",
      description: "Giao hàng trong 1-2 ngày làm việc",
      type: "EXPRESS",
      baseFee: 50000,
      estimatedDays: "1-2 ngày",
      freeShippingMin: 800000, // Free ship từ 800k
      isActive: true,
    },
    {
      name: "Giao hàng hỏa tốc",
      description: "Giao hàng trong ngày (chỉ nội thành)",
      type: "SAME_DAY",
      baseFee: 80000,
      estimatedDays: "Trong ngày",
      freeShippingMin: 1000000, // Free ship từ 1M
      isActive: true,
    },
  ];

  let createdCount = 0;

  // Tạo shipping methods cho mỗi zone
  for (const zone of zones) {
    for (const method of shippingMethods) {
      // Điều chỉnh giá theo zone
      let adjustedBaseFee = method.baseFee;
      if (zone.name.includes("Miền")) {
        adjustedBaseFee = method.baseFee * 1.5; // Tăng 50% cho miền xa
      } else if (zone.name.includes("ngoại thành")) {
        adjustedBaseFee = method.baseFee * 1.2; // Tăng 20% cho ngoại thành
      }

      await prisma.shippingMethod.create({
        data: {
          ...method,
          type: method.type as any,
          baseFee: adjustedBaseFee,
          zoneId: zone.id,
        },
      });
      createdCount++;
    }
  }

  console.log(`✅ Shipping methods seeded successfully: ${createdCount} items`);
}

// ===== REVIEW MEDIA SEEDING =====
async function seedReviewMedia() {
  console.log("🌱 Seeding review media...");

  // Lấy reviews để thêm media
  const reviews = await prisma.review.findMany({
    take: 50, // Chỉ lấy 50 reviews đầu tiên
  });

  if (reviews.length === 0) {
    console.log("⚠️ No reviews found. Skipping review media seeding.");
    return;
  }

  const reviewImages = [
    "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1506629905607-d405b7a82b0b?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1483985988355-763728e1935b?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1490481651871-ab68de25d43d?w=400&h=400&fit=crop",
  ];

  let createdCount = 0;

  // Thêm 1-3 ảnh cho mỗi review (30% reviews có ảnh)
  for (const review of reviews) {
    if (Math.random() < 0.3) {
      // 30% chance có ảnh
      const numImages = Math.floor(Math.random() * 3) + 1; // 1-3 ảnh

      for (let i = 0; i < numImages; i++) {
        const imageUrl =
          reviewImages[Math.floor(Math.random() * reviewImages.length)];

        // Tạo Media record trước
        const media = await prisma.media.create({
          data: {
            filename: `review-${review.id}-${i + 1}.jpg`,
            path: `/reviews/review-${review.id}-${i + 1}.jpg`,
            url: imageUrl,
            mimeType: "image/jpeg",
            size: Math.floor(Math.random() * 500000) + 100000, // 100KB - 600KB
            width: 400,
            height: 400,
            alt: `Review image ${i + 1}`,
            title: `Review image ${i + 1}`,
            folder: "reviews",
            type: "EXTERNAL",
            externalUrl: imageUrl,
          },
        });

        // Tạo ReviewMedia relationship
        await prisma.reviewMedia.create({
          data: {
            reviewId: review.id,
            mediaId: media.id,
            order: i,
          },
        });
        createdCount++;
      }
    }
  }

  console.log(`✅ Review media seeded successfully: ${createdCount} items`);
}

// ===== MAIN SEEDING FUNCTION =====
async function main() {
  console.log("🚀 Starting NS Shop database seeding (New Version)...");

  try {
    // Clear existing data first (optional - comment out if you want to keep existing data)
    console.log("🧹 Clearing existing data...");

    // Delete in correct order to avoid foreign key constraints
    await prisma.stockMovement.deleteMany();
    await prisma.inventoryEntry.deleteMany();
    await prisma.productMedia.deleteMany();
    await prisma.productAttribute.deleteMany();
    await prisma.cartItem.deleteMany();
    await prisma.cart.deleteMany();
    await prisma.orderItem.deleteMany();
    await prisma.order.deleteMany();
    await prisma.review.deleteMany();
    await prisma.wishlistItem.deleteMany();
    await prisma.address.deleteMany();
    await prisma.product.deleteMany();
    await prisma.attributeValue.deleteMany();
    await prisma.attribute.deleteMany();

    // Delete content that references admin users
    await prisma.auditLog.deleteMany();
    await prisma.notification.deleteMany();
    await prisma.event.deleteMany();
    await prisma.page.deleteMany();
    await prisma.post.deleteMany();
    await prisma.menu.deleteMany();

    await prisma.category.deleteMany();
    await prisma.brand.deleteMany();
    await prisma.media.deleteMany();
    await prisma.user.deleteMany();
    await prisma.setting.deleteMany();
    await prisma.emailTemplate.deleteMany();
    await prisma.adminUser.deleteMany();

    // Seed data in correct order
    const { admin, moderator } = await seedBasicData();
    await seedSettings();
    const mediaItems = await seedMedia();
    const brands = await seedBrands(mediaItems);
    const categories = await seedCategories(mediaItems);
    const attributes = await seedAttributes();
    const users = await seedUsers();
    const products = await seedProducts(mediaItems, categories, brands);
    await seedInventory();
    await seedProductAttributes();
    await seedCartItems();
    await seedWishlistItems();
    await seedOrders();
    await seedReviews();
    await seedMenus();
    await seedPages();
    await seedPosts();
    await seedPromotions();
    await seedPaymentGateways();
    await seedShippingZones();
    await seedShippingMethods();
    await seedReviewMedia();
    await seedEmailTemplates();

    console.log("✅ Database seeding completed successfully!");
    console.log("\n📧 Login credentials:");
    console.log("Admin: <EMAIL> / admin123");
    console.log("Moderator: <EMAIL> / moderator123");
    console.log("Users: <EMAIL> / user1123 (user1-user20)");
    console.log("\n📊 Seeded data summary:");
    console.log("- 2 admin accounts (1 admin, 1 moderator)");
    console.log("- 20 user accounts with addresses and carts");
    console.log(`- ${brands.length} brands with logos`);
    console.log(`- ${categories.length} categories (6 main + subcategories)`);
    console.log(`- ${attributes.length} attributes with values`);
    console.log(`- ${products.length} realistic Vietnamese fashion products`);
    console.log("- Product attributes linking products to sizes, colors, etc.");
    console.log("- Cart items for testing shopping flow");
    console.log("- Wishlist items for users");
    console.log("- Order history with order items");
    console.log("- Product reviews from users");
    console.log("- Navigation menus for header and footer");
    console.log("- Static pages (About, Contact, Privacy Policy, etc.)");
    console.log("- Inventory entries with stock movements");
    console.log("- Media files for brands, categories, and products");
    console.log("- Email templates");
    console.log("- System settings");
  } catch (error) {
    console.error("❌ Database seeding failed:", error);
    process.exit(1);
  }
}

main()
  .catch((e) => {
    console.error("❌ Seed error:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
