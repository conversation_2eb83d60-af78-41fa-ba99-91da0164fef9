/**
 * Products Seeder
 * Creates products with variants, attributes, media, and inventory
 */

import { BaseSeeder } from "./lib/base-seeder";

export class ProductsSeeder extends BaseSeeder {
  getName(): string {
    return "Products";
  }

  async seed(): Promise<any[]> {
    this.logStart();

    // Get required data
    const categories = await this.getAllRecords(this.prisma.category, {
      parentId: { not: null },
    });
    const brands = await this.getAllRecords(this.prisma.brand);
    const attributes = await this.getAllRecords(this.prisma.attribute);
    const attributeValues = await this.getAllRecords(
      this.prisma.attributeValue
    );
    const productMedia = await this.prisma.media.findMany({
      where: { folder: "products" },
    });

    if (categories.length === 0 || brands.length === 0) {
      this.logWarning(
        "Missing required data. Please run previous seeders first."
      );
      return [];
    }

    const productCount = 50; // Create 50 products
    const createdProducts = [];

    for (let i = 1; i <= productCount; i++) {
      const category = this.dataGenerator.randomChoice(categories);
      const brand = this.dataGenerator.randomChoice(brands);

      // Generate product data
      const productName = this.dataGenerator.generateProductName(category.slug);
      const price = this.dataGenerator.generatePrice(category.slug);
      const comparePrice = this.dataGenerator.randomBoolean(0.3)
        ? price + this.dataGenerator.randomInt(50000, 200000)
        : null;

      const product = await this.prisma.product.create({
        data: {
          name: productName,
          slug: await this.generateUniqueSlug(
            this.prisma.product,
            this.dataGenerator.generateSlug(productName)
          ),
          description:
            this.dataGenerator.generateProductDescription(productName),
          sku: await this.generateUniqueCode(
            this.prisma.product,
            `PRD${i.toString().padStart(3, "0")}`,
            "sku"
          ),
          price: price,
          salePrice: comparePrice,
          stock: this.dataGenerator.randomInt(0, 100),
          featured: this.dataGenerator.randomBoolean(0.2),
          status: this.dataGenerator.randomBoolean(0.9) ? "ACTIVE" : "INACTIVE",
          categoryId: category.id,
          brandId: brand.id,
          tags: [
            category.name.toLowerCase(),
            brand.name.toLowerCase(),
            this.dataGenerator.randomChoice([
              "new",
              "trending",
              "bestseller",
              "sale",
            ]),
          ],
        },
      });

      createdProducts.push(product);

      // Add product attributes
      await this.addProductAttributes(product, attributes, attributeValues);

      // Add product media
      await this.addProductMedia(product, productMedia);

      // Create inventory
      await this.createProductInventory(product);

      // Progress indicator
      if (i % 10 === 0) {
        console.log(`   └── Created ${i}/${productCount} products...`);
      }
    }

    this.logSuccess(createdProducts.length, "products");

    // Show additional stats
    const productAttributeCount = await this.prisma.productAttribute.count();
    const productMediaCount = await this.prisma.productMedia.count();
    const inventoryCount = await this.prisma.inventoryEntry.count();

    console.log(`   └── Created ${productAttributeCount} product attributes`);
    console.log(`   └── Created ${productMediaCount} product media`);
    console.log(`   └── Created ${inventoryCount} inventory entries`);

    return createdProducts;
  }

  private async addProductAttributes(
    product: any,
    attributes: any[],
    attributeValues: any[]
  ) {
    // Add size attribute (most products have size)
    const sizeAttribute = attributes.find((attr) => attr.slug === "kich-thuoc");
    if (sizeAttribute) {
      const sizeValues = attributeValues.filter(
        (val) => val.attributeId === sizeAttribute.id
      );
      const selectedSize = this.dataGenerator.randomChoice(sizeValues);

      await this.prisma.productAttribute.create({
        data: {
          productId: product.id,
          attributeId: sizeAttribute.id,
          attributeValueId: selectedSize.id,
        },
      });
    }

    // Add color attribute (80% chance)
    if (this.dataGenerator.randomBoolean(0.8)) {
      const colorAttribute = attributes.find((attr) => attr.slug === "mau-sac");
      if (colorAttribute) {
        const colorValues = attributeValues.filter(
          (val) => val.attributeId === colorAttribute.id
        );
        const selectedColor = this.dataGenerator.randomChoice(colorValues);

        await this.prisma.productAttribute.create({
          data: {
            productId: product.id,
            attributeId: colorAttribute.id,
            attributeValueId: selectedColor.id,
          },
        });
      }
    }

    // Add material attribute (60% chance)
    if (this.dataGenerator.randomBoolean(0.6)) {
      const materialAttribute = attributes.find(
        (attr) => attr.slug === "chat-lieu"
      );
      if (materialAttribute) {
        const materialValues = attributeValues.filter(
          (val) => val.attributeId === materialAttribute.id
        );
        const selectedMaterial =
          this.dataGenerator.randomChoice(materialValues);

        await this.prisma.productAttribute.create({
          data: {
            productId: product.id,
            attributeId: materialAttribute.id,
            attributeValueId: selectedMaterial.id,
          },
        });
      }
    }

    // Add style attribute (40% chance)
    if (this.dataGenerator.randomBoolean(0.4)) {
      const styleAttribute = attributes.find(
        (attr) => attr.slug === "phong-cach"
      );
      if (styleAttribute) {
        const styleValues = attributeValues.filter(
          (val) => val.attributeId === styleAttribute.id
        );
        const selectedStyle = this.dataGenerator.randomChoice(styleValues);

        await this.prisma.productAttribute.create({
          data: {
            productId: product.id,
            attributeId: styleAttribute.id,
            attributeValueId: selectedStyle.id,
          },
        });
      }
    }

    // Add gender attribute (70% chance)
    if (this.dataGenerator.randomBoolean(0.7)) {
      const genderAttribute = attributes.find(
        (attr) => attr.slug === "gioi-tinh"
      );
      if (genderAttribute) {
        const genderValues = attributeValues.filter(
          (val) => val.attributeId === genderAttribute.id
        );
        const selectedGender = this.dataGenerator.randomChoice(genderValues);

        await this.prisma.productAttribute.create({
          data: {
            productId: product.id,
            attributeId: genderAttribute.id,
            attributeValueId: selectedGender.id,
          },
        });
      }
    }
  }

  private async addProductMedia(product: any, productMedia: any[]) {
    // Each product gets 2-5 images
    const imageCount = this.dataGenerator.randomInt(2, 5);
    const selectedMedia = this.dataGenerator.randomChoices(
      productMedia,
      imageCount
    );

    for (let i = 0; i < selectedMedia.length; i++) {
      await this.prisma.productMedia.create({
        data: {
          productId: product.id,
          mediaId: selectedMedia[i].id,
          isPrimary: i === 0, // First image is primary
          order: i,
        },
      });
    }
  }

  private async createProductInventory(product: any) {
    const quantity = this.dataGenerator.randomInt(0, 100);
    const reservedQuantity = Math.min(
      quantity,
      this.dataGenerator.randomInt(0, 10)
    );

    const inventoryEntry = await this.prisma.inventoryEntry.create({
      data: {
        productId: product.id,
        location: "MAIN_WAREHOUSE",
        quantity: quantity,
        reserved: reservedQuantity,
        available: quantity - reservedQuantity,
        minStock: this.dataGenerator.randomInt(5, 20),
        maxStock: this.dataGenerator.randomInt(100, 200),
      },
    });

    // Create initial stock movement
    if (quantity > 0) {
      await this.prisma.stockMovement.create({
        data: {
          inventoryEntryId: inventoryEntry.id,
          type: "IN",
          quantity: quantity,
          reason: "Initial stock setup",
          notes: "Initial inventory setup",
        },
      });
    }
  }
}
