/**
 * Attributes Seeder
 * Creates product attributes and their values
 */

import { BaseSeeder } from "./lib/base-seeder";

export class AttributesSeeder extends BaseSeeder {
  getName(): string {
    return "Attributes";
  }

  async seed(): Promise<any[]> {
    this.logStart();

    const attributesData = [
      {
        name: "<PERSON><PERSON><PERSON> thước",
        slug: "kich-thuoc",
        type: "SELECT",
        values: ["XS", "S", "M", "L", "XL", "XXL"],
      },
      {
        name: "<PERSON><PERSON><PERSON> sắc",
        slug: "mau-sac",
        type: "COLOR",
        values: [
          "Đen",
          "Trắng",
          "Xanh navy",
          "Xanh dương",
          "Đỏ",
          "<PERSON>ồng",
          "Vàng",
          "<PERSON>ám",
          "Nâu",
          "Xanh lá",
        ],
      },
      {
        name: "<PERSON>ất liệu",
        slug: "chat-lieu",
        type: "SELECT",
        values: ["<PERSON>", "Polyester", "Linen", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"],
      },
      {
        name: "<PERSON><PERSON> cách",
        slug: "phong-cach",
        type: "SELECT",
        values: [
          "Casual",
          "Formal",
          "Vintage",
          "Sporty",
          "Bohemian",
          "Minimalist",
        ],
      },
      {
        name: "Giới tính",
        slug: "gioi-tinh",
        type: "SELECT",
        values: ["Nam", "Nữ", "Unisex"],
      },
    ];

    const createdAttributes = [];

    for (const attrData of attributesData) {
      // Create or update attribute
      const attribute = await this.upsertRecord(
        this.prisma.attribute,
        { slug: attrData.slug },
        {
          name: attrData.name,
          slug: attrData.slug,
          type: attrData.type as any,
        }
      );
      createdAttributes.push(attribute);

      // Create attribute values
      for (let i = 0; i < attrData.values.length; i++) {
        const value = attrData.values[i];
        const slug = this.dataGenerator.generateSlug(value);
        await this.upsertRecord(
          this.prisma.attributeValue,
          {
            attributeId_slug: {
              attributeId: attribute.id,
              slug: slug,
            },
          },
          {
            attributeId: attribute.id,
            value: value,
            slug: slug,
            sortOrder: i,
          }
        );
      }
    }

    // Count total attribute values created
    const totalValues = await this.prisma.attributeValue.count();

    this.logSuccess(createdAttributes.length, "attributes");
    console.log(`   └── Created ${totalValues} attribute values`);

    return createdAttributes;
  }
}
