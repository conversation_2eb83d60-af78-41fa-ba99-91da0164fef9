/**
 * Data Generator Library for NS Shop
 * Provides utilities to generate realistic Vietnamese e-commerce data
 */

import { faker } from "@faker-js/faker";

export class DataGenerator {
  // ===== RANDOM UTILITIES =====
  static randomInt(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  static randomFloat(min: number, max: number, decimals: number = 2): number {
    return parseFloat((Math.random() * (max - min) + min).toFixed(decimals));
  }

  static randomChoice<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }

  static randomChoices<T>(array: T[], count: number): T[] {
    const shuffled = [...array].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, array.length));
  }

  static randomBoolean(probability: number = 0.5): boolean {
    return Math.random() < probability;
  }

  // ===== IMAGE GENERATORS =====
  static generateImageUrl(
    category: string,
    width: number = 400,
    height: number = 400
  ): string {
    const imageCategories = {
      fashion: [
        "https://images.unsplash.com/photo-1441986300917-64674bd600d8",
        "https://images.unsplash.com/photo-1469334031218-e382a71b716b",
        "https://images.unsplash.com/photo-1515372039744-b8f02a3ae446",
        "https://images.unsplash.com/photo-1506629905607-d405b7a82b0b",
        "https://images.unsplash.com/photo-1434389677669-e08b4cac3105",
        "https://images.unsplash.com/photo-1445205170230-053b83016050",
        "https://images.unsplash.com/photo-1483985988355-763728e1935b",
        "https://images.unsplash.com/photo-1490481651871-ab68de25d43d",
      ],
      brands: [
        "https://images.unsplash.com/photo-1560472354-b33ff0c44a43",
        "https://images.unsplash.com/photo-1586023492125-27b2c045efd7",
        "https://images.unsplash.com/photo-1441986300917-64674bd600d8",
        "https://images.unsplash.com/photo-1469334031218-e382a71b716b",
      ],
      categories: [
        "https://images.unsplash.com/photo-1441986300917-64674bd600d8",
        "https://images.unsplash.com/photo-1469334031218-e382a71b716b",
        "https://images.unsplash.com/photo-1515372039744-b8f02a3ae446",
        "https://images.unsplash.com/photo-1506629905607-d405b7a82b0b",
      ],
      reviews: [
        "https://images.unsplash.com/photo-1441986300917-64674bd600d8",
        "https://images.unsplash.com/photo-1469334031218-e382a71b716b",
        "https://images.unsplash.com/photo-1515372039744-b8f02a3ae446",
        "https://images.unsplash.com/photo-1506629905607-d405b7a82b0b",
      ],
    };

    const baseUrl = this.randomChoice(
      imageCategories[category as keyof typeof imageCategories] ||
        imageCategories.fashion
    );
    return `${baseUrl}?w=${width}&h=${height}&fit=crop&auto=format`;
  }

  // ===== VIETNAMESE TEXT GENERATORS =====
  static generateVietnameseName(): string {
    const firstNames = [
      "Nguyễn",
      "Trần",
      "Lê",
      "Phạm",
      "Hoàng",
      "Huỳnh",
      "Phan",
      "Vũ",
      "Võ",
      "Đặng",
      "Bùi",
      "Đỗ",
      "Hồ",
      "Ngô",
      "Dương",
      "Lý",
      "Đinh",
      "Đào",
      "Lương",
      "Vương",
    ];

    const lastNames = [
      "Văn Minh",
      "Thị Lan",
      "Văn Hùng",
      "Thị Mai",
      "Văn Đức",
      "Thị Hoa",
      "Văn Tuấn",
      "Thị Linh",
      "Văn Nam",
      "Thị Nga",
      "Văn Khoa",
      "Thị Thảo",
      "Văn Long",
      "Thị Hương",
      "Văn Phong",
      "Thị Trang",
      "Văn Quang",
      "Thị Yến",
    ];

    return `${this.randomChoice(firstNames)} ${this.randomChoice(lastNames)}`;
  }

  static generateVietnameseAddress(): {
    street: string;
    ward: string;
    district: string;
    province: string;
    fullAddress: string;
  } {
    const streets = [
      "Nguyễn Trãi",
      "Lê Lợi",
      "Trần Hưng Đạo",
      "Hai Bà Trưng",
      "Nguyễn Huệ",
      "Điện Biên Phủ",
      "Cách Mạng Tháng 8",
      "Lý Thường Kiệt",
      "Nguyễn Thái Học",
    ];

    const wards = [
      "Phường 1",
      "Phường 2",
      "Phường 3",
      "Phường Bến Nghé",
      "Phường Đa Kao",
      "Phường Nguyễn Thái Bình",
      "Phường Phạm Ngũ Lão",
      "Phường Cầu Ông Lãnh",
    ];

    const districts = [
      "Quận 1",
      "Quận 3",
      "Quận 5",
      "Quận 7",
      "Quận Bình Thạnh",
      "Quận Tân Bình",
      "Quận Phú Nhuận",
      "Quận Gò Vấp",
    ];

    const provinces = [
      "TP. Hồ Chí Minh",
      "Hà Nội",
      "Đà Nẵng",
      "Hải Phòng",
      "Cần Thơ",
    ];

    const streetNumber = this.randomInt(1, 999);
    const street = this.randomChoice(streets);
    const ward = this.randomChoice(wards);
    const district = this.randomChoice(districts);
    const province = this.randomChoice(provinces);

    return {
      street: `${streetNumber} ${street}`,
      ward,
      district,
      province,
      fullAddress: `${streetNumber} ${street}, ${ward}, ${district}, ${province}`,
    };
  }

  static generateProductName(category: string): string {
    const productNames = {
      "ao-thun": [
        "Áo thun cotton basic",
        "Áo thun polo nam",
        "Áo thun nữ form rộng",
        "Áo thun vintage",
        "Áo thun graphic tee",
        "Áo thun oversize",
      ],
      "vay-dam": [
        "Váy maxi hoa nhí",
        "Đầm suông công sở",
        "Váy xòe vintage",
        "Đầm bodycon",
        "Váy midi thanh lịch",
        "Đầm dạ hội",
      ],
      "quan-jeans": [
        "Quần jeans skinny",
        "Quần jeans boyfriend",
        "Quần jeans rách gối",
        "Quần jeans ống rộng",
        "Quần jeans cao cấp",
        "Quần jeans vintage",
      ],
      "ao-khoac": [
        "Áo khoác bomber",
        "Áo blazer nữ",
        "Áo khoác denim",
        "Áo khoác da",
        "Áo cardigan",
        "Áo hoodie",
      ],
      "phu-kien": [
        "Túi xách da",
        "Ví cầm tay",
        "Thắt lưng da",
        "Khăn choàng lụa",
        "Mũ bucket",
        "Kính mát",
      ],
      "giay-dep": [
        "Giày sneaker",
        "Giày cao gót",
        "Giày oxford",
        "Dép sandal",
        "Giày boot",
        "Giày thể thao",
      ],
    };

    const names =
      productNames[category as keyof typeof productNames] ||
      productNames["ao-thun"];
    const baseName = this.randomChoice(names);
    const colors = ["đen", "trắng", "xanh", "đỏ", "vàng", "hồng", "nâu", "xám"];
    const materials = ["cotton", "linen", "polyester", "silk", "denim", "da"];

    const color = this.randomChoice(colors);
    const material = this.randomChoice(materials);

    return `${baseName} ${color} ${material}`;
  }

  static generateProductDescription(productName: string): string {
    const descriptions = [
      `${productName} được thiết kế với chất liệu cao cấp, mang lại sự thoải mái tối đa cho người mặc.`,
      `Sản phẩm ${productName} với thiết kế hiện đại, phù hợp cho nhiều dịp khác nhau.`,
      `${productName} chất lượng premium, được gia công tỉ mỉ từng chi tiết.`,
      `Thiết kế ${productName} trendy, phù hợp với xu hướng thời trang hiện tại.`,
      `${productName} với form dáng chuẩn, tôn lên vẻ đẹp tự nhiên của người mặc.`,
    ];

    return this.randomChoice(descriptions);
  }

  // ===== PRICE GENERATORS =====
  static generatePrice(category: string): number {
    const priceRanges = {
      "ao-thun": [150000, 500000],
      "vay-dam": [300000, 800000],
      "quan-jeans": [400000, 900000],
      "ao-khoac": [500000, 1200000],
      "phu-kien": [100000, 600000],
      "giay-dep": [300000, 1000000],
    };

    const range = priceRanges[category as keyof typeof priceRanges] || [
      200000, 600000,
    ];
    return this.randomInt(range[0], range[1]);
  }

  // ===== EMAIL GENERATORS =====
  static generateEmail(name?: string): string {
    const domains = ["gmail.com", "yahoo.com", "hotmail.com", "outlook.com"];
    const domain = this.randomChoice(domains);

    if (name) {
      const cleanName = name
        .toLowerCase()
        .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
        .replace(/[èéẹẻẽêềếệểễ]/g, "e")
        .replace(/[ìíịỉĩ]/g, "i")
        .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
        .replace(/[ùúụủũưừứựửữ]/g, "u")
        .replace(/[ỳýỵỷỹ]/g, "y")
        .replace(/đ/g, "d")
        .replace(/[^a-z0-9]/g, "");

      return `${cleanName}${this.randomInt(1, 999)}@${domain}`;
    }

    return faker.internet.email().toLowerCase();
  }

  // ===== PHONE GENERATORS =====
  static generatePhoneNumber(): string {
    const prefixes = ["090", "091", "094", "083", "084", "085", "081", "082"];
    const prefix = this.randomChoice(prefixes);
    const number = this.randomInt(1000000, 9999999);
    return `${prefix}${number}`;
  }

  // ===== DATE GENERATORS =====
  static generatePastDate(days: number): Date {
    const now = new Date();
    const pastDate = new Date(
      now.getTime() - Math.random() * days * 24 * 60 * 60 * 1000
    );
    return pastDate;
  }

  static generateFutureDate(days: number): Date {
    const now = new Date();
    const futureDate = new Date(
      now.getTime() + Math.random() * days * 24 * 60 * 60 * 1000
    );
    return futureDate;
  }

  // ===== SLUG GENERATORS =====
  static generateSlug(text: string): string {
    return text
      .toLowerCase()
      .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
      .replace(/[èéẹẻẽêềếệểễ]/g, "e")
      .replace(/[ìíịỉĩ]/g, "i")
      .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
      .replace(/[ùúụủũưừứựửữ]/g, "u")
      .replace(/[ỳýỵỷỹ]/g, "y")
      .replace(/đ/g, "d")
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();
  }

  // ===== REVIEW GENERATORS =====
  static generateReviewContent(rating: number): string {
    const positiveReviews = [
      "Sản phẩm rất đẹp, chất lượng tốt, giao hàng nhanh!",
      "Mình rất hài lòng với sản phẩm này. Chất liệu tốt, form dáng đẹp.",
      "Đóng gói cẩn thận, sản phẩm đúng như mô tả. Sẽ ủng hộ shop tiếp!",
      "Chất lượng vượt mong đợi, giá cả hợp lý. Recommend!",
      "Sản phẩm đẹp, chất liệu tốt, size chuẩn. Very good!",
    ];

    const neutralReviews = [
      "Sản phẩm ổn, đúng như mô tả. Giao hàng hơi chậm.",
      "Chất lượng tạm được, giá cả hợp lý.",
      "Sản phẩm bình thường, không có gì đặc biệt.",
      "Đúng như hình, chất lượng tạm ổn.",
    ];

    const negativeReviews = [
      "Sản phẩm không đúng như mô tả, chất lượng kém.",
      "Giao hàng chậm, sản phẩm không như kỳ vọng.",
      "Chất liệu không tốt, form dáng không đẹp.",
      "Không hài lòng với sản phẩm này.",
    ];

    if (rating >= 4) {
      return this.randomChoice(positiveReviews);
    } else if (rating >= 3) {
      return this.randomChoice(neutralReviews);
    } else {
      return this.randomChoice(negativeReviews);
    }
  }
}
