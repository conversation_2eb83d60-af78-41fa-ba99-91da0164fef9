/**
 * Main Seeder Entry Point
 * Orchestrates all seeding operations
 */

import { PrismaClient } from "@prisma/client";
import { AdminUserSeeder } from "./admin-user.seeder";
import { SettingsSeeder } from "./settings.seeder";
import { MediaSeeder } from "./media.seeder";
import { BrandsSeeder } from "./brands.seeder";
import { CategoriesSeeder } from "./categories.seeder";
import { AttributesSeeder } from "./attributes.seeder";
import { UsersSeeder } from "./users.seeder";
import { ProductsSeeder } from "./products.seeder";

const prisma = new PrismaClient();

async function main() {
  console.log("🌱 Starting NS Shop Database Seeding...\n");

  try {
    // Phase 1: Core System Data
    console.log("📋 Phase 1: Core System Data");
    console.log("================================");

    const adminUserSeeder = new AdminUserSeeder(prisma);
    await adminUserSeeder.seed();

    const settingsSeeder = new SettingsSeeder(prisma);
    await settingsSeeder.seed();

    console.log("");

    // Phase 2: Media Assets
    console.log("🖼️  Phase 2: Media Assets");
    console.log("================================");

    const mediaSeeder = new MediaSeeder(prisma);
    await mediaSeeder.seed();

    console.log("");

    // Phase 3: Product Catalog Foundation
    console.log("🏗️  Phase 3: Product Catalog Foundation");
    console.log("================================");

    const brandsSeeder = new BrandsSeeder(prisma);
    await brandsSeeder.seed();

    const categoriesSeeder = new CategoriesSeeder(prisma);
    await categoriesSeeder.seed();

    const attributesSeeder = new AttributesSeeder(prisma);
    await attributesSeeder.seed();

    console.log("");

    // Phase 4: Users and Customers
    console.log("👥 Phase 4: Users and Customers");
    console.log("================================");

    const usersSeeder = new UsersSeeder(prisma);
    await usersSeeder.seed();

    console.log("");

    // Phase 5: Products and Inventory
    console.log("🛍️  Phase 5: Products and Inventory");
    console.log("================================");

    const productsSeeder = new ProductsSeeder(prisma);
    await productsSeeder.seed();

    console.log("");

    // Summary
    console.log("✅ SEEDING COMPLETED SUCCESSFULLY!");
    console.log("================================");

    // Show summary statistics
    const stats = await getDatabaseStats();
    console.log("📊 Database Statistics:");
    console.log(`   • Admin Users: ${stats.adminUsers}`);
    console.log(`   • Settings: ${stats.settings}`);
    console.log(`   • Media: ${stats.media}`);
    console.log(`   • Brands: ${stats.brands}`);
    console.log(`   • Categories: ${stats.categories}`);
    console.log(`   • Attributes: ${stats.attributes}`);
    console.log(`   • Attribute Values: ${stats.attributeValues}`);
    console.log(`   • Users: ${stats.users}`);
    console.log(`   • Addresses: ${stats.addresses}`);
    console.log(`   • Products: ${stats.products}`);
    console.log(`   • Product Attributes: ${stats.productAttributes}`);
    console.log(`   • Product Media: ${stats.productMedia}`);
    console.log(`   • Inventory Entries: ${stats.inventoryEntries}`);

    console.log("\n🎉 Ready to seed products, orders, and other data!");
    console.log("💡 Next steps:");
    console.log("   1. Run product seeder");
    console.log("   2. Run order seeder");
    console.log("   3. Run review seeder");
    console.log("   4. Test the application");
  } catch (error) {
    console.error("❌ Seeding failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

async function getDatabaseStats() {
  const [
    adminUsers,
    settings,
    media,
    brands,
    categories,
    attributes,
    attributeValues,
    users,
    addresses,
    products,
    productAttributes,
    productMedia,
    inventoryEntries,
  ] = await Promise.all([
    prisma.adminUser.count(),
    prisma.setting.count(),
    prisma.media.count(),
    prisma.brand.count(),
    prisma.category.count(),
    prisma.attribute.count(),
    prisma.attributeValue.count(),
    prisma.user.count(),
    prisma.address.count(),
    prisma.product.count(),
    prisma.productAttribute.count(),
    prisma.productMedia.count(),
    prisma.inventoryEntry.count(),
  ]);

  return {
    adminUsers,
    settings,
    media,
    brands,
    categories,
    attributes,
    attributeValues,
    users,
    addresses,
    products,
    productAttributes,
    productMedia,
    inventoryEntries,
  };
}

// Handle unhandled promise rejections
process.on("unhandledRejection", (err) => {
  console.error("Unhandled Promise Rejection:", err);
  process.exit(1);
});

// Run the seeder
main();
