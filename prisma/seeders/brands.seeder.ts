/**
 * Brands Seeder
 * Creates fashion brands with logos
 */

import { BaseSeeder } from "./lib/base-seeder";

export class BrandsSeeder extends BaseSeeder {
  getName(): string {
    return "Brands";
  }

  async seed(): Promise<any[]> {
    this.logStart();

    // Get brand logo media
    const brandLogos = await this.prisma.media.findMany({
      where: { folder: "brands" },
    });

    if (brandLogos.length === 0) {
      this.logWarning("No brand logos found. Please run MediaSeeder first.");
      return [];
    }

    const brandData = [
      {
        name: "<PERSON><PERSON>",
        description: "Thương hiệu thời trang nhanh từ Tây Ban Nha",
        website: "https://www.zara.com",
        isActive: true,
      },
      {
        name: "H&M",
        description: "Thời trang bền vững và phong cách Bắc Âu",
        website: "https://www.hm.com",
        isActive: true,
      },
      {
        name: "Uniqlo",
        description: "Thời trang tối giản và chất lượng từ <PERSON>ả<PERSON>",
        website: "https://www.uniqlo.com",
        isActive: true,
      },
      {
        name: "<PERSON>",
        description: "Thương hiệu thể thao hàng đầu thế giới",
        website: "https://www.nike.com",
        isActive: true,
      },
      {
        name: "Adidas",
        description: "Thời trang thể thao và lifestyle",
        website: "https://www.adidas.com",
        isActive: true,
      },
    ];

    const createdBrands = [];
    for (let i = 0; i < brandData.length; i++) {
      const brand = brandData[i];
      const logo = brandLogos[i % brandLogos.length];

      const slug = this.dataGenerator.generateSlug(brand.name);
      const createdBrand = await this.upsertRecord(
        this.prisma.brand,
        { slug: slug },
        {
          ...brand,
          slug: slug,
          logoId: logo.id,
        }
      );
      createdBrands.push(createdBrand);
    }

    this.logSuccess(createdBrands.length, "brands");
    return createdBrands;
  }
}
