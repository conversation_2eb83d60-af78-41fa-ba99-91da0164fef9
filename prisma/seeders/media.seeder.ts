/**
 * Media Seeder
 * Creates media records for brands, categories, and products
 */

import { BaseSeeder } from './lib/base-seeder';

export class MediaSeeder extends BaseSeeder {
  getName(): string {
    return 'Media';
  }

  async seed(): Promise<any[]> {
    this.logStart();

    const mediaRecords = [];

    // Brand logos
    const brandLogos = [
      { name: 'zara-logo.jpg', folder: 'brands' },
      { name: 'h&m-logo.jpg', folder: 'brands' },
      { name: 'uniqlo-logo.jpg', folder: 'brands' },
      { name: 'nike-logo.jpg', folder: 'brands' },
      { name: 'adidas-logo.jpg', folder: 'brands' },
    ];

    for (const logo of brandLogos) {
      const media = await this.createMediaRecord({
        filename: logo.name,
        folder: logo.folder,
        url: this.dataGenerator.generateImageUrl('brands', 200, 200),
        alt: `${logo.name} logo`,
        title: `${logo.name} logo`,
        width: 200,
        height: 200,
      });
      mediaRecords.push(media);
    }

    // Category images
    const categories = [
      'ao-thun', 'vay-dam', 'quan-jeans', 'ao-khoac', 'phu-kien', 'giay-dep'
    ];

    for (const category of categories) {
      // Main category image
      const mainImage = await this.createMediaRecord({
        filename: `${category}-main.jpg`,
        folder: 'categories',
        url: this.dataGenerator.generateImageUrl('categories', 400, 300),
        alt: `${category} category`,
        title: `${category} category`,
        width: 400,
        height: 300,
      });
      mediaRecords.push(mainImage);

      // Banner image
      const bannerImage = await this.createMediaRecord({
        filename: `${category}-banner.jpg`,
        folder: 'categories',
        url: this.dataGenerator.generateImageUrl('categories', 800, 400),
        alt: `${category} banner`,
        title: `${category} banner`,
        width: 800,
        height: 400,
      });
      mediaRecords.push(bannerImage);
    }

    // Product images (will be used by product seeder)
    const productImageCount = 80; // Generate 80 product images
    for (let i = 1; i <= productImageCount; i++) {
      const media = await this.createMediaRecord({
        filename: `product-${i}.jpg`,
        folder: 'products',
        url: this.dataGenerator.generateImageUrl('fashion'),
        alt: `Product image ${i}`,
        title: `Product image ${i}`,
      });
      mediaRecords.push(media);
    }

    this.logSuccess(mediaRecords.length, 'media records');
    return mediaRecords;
  }
}
