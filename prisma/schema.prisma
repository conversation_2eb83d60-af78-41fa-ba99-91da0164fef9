// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model - Regular customers only
model User {
  id          String    @id @default(cuid())
  email       String    @unique
  name        String
  password    String
  avatarId    String? // Reference to Media table
  phone       String?
  dateOfBirth DateTime?
  gender      Gender?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  orders             Order[]
  addresses          Address[]
  reviews            Review[]
  cart               Cart?
  passwordResetToken PasswordResetToken?
  wishlistItems      WishlistItem[]
  avatar             Media?              @relation("UserAvatar", fields: [avatarId], references: [id])

  @@map("users")
}

// Wishlist model
model WishlistItem {
  id        String   @id @default(cuid())
  userId    String
  productId String
  createdAt DateTime @default(now())

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("wishlist_items")
}

// Password reset token model
model PasswordResetToken {
  id        String   @id @default(cuid())
  userId    String   @unique
  token     String   @unique
  expiresAt DateTime
  used      Boolean  @default(false)
  createdAt DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("password_reset_tokens")
}

// AdminUser model - Admin and moderator accounts
model AdminUser {
  id          String    @id @default(cuid())
  email       String    @unique
  name        String
  password    String
  role        AdminRole @default(MODERATOR)
  avatarId    String? // Reference to Media table
  phone       String?
  isActive    Boolean   @default(true)
  permissions Json? // Fine-grained permissions for moderators
  lastLoginAt DateTime?
  department  String? // Organization department
  createdBy   String? // Which admin created this account
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  createdByAdmin        AdminUser?     @relation("AdminHierarchy", fields: [createdBy], references: [id])
  createdAdmins         AdminUser[]    @relation("AdminHierarchy")
  posts                 Post[]
  pages                 Page[]
  createdNotifications  Notification[] @relation("NotificationCreator")
  targetedNotifications Notification[] @relation("NotificationTarget")
  auditLogs             AuditLog[]     @relation("AuditLogAdmin")
  createdEvents         Event[]        @relation("EventCreator")
  avatar                Media?         @relation("AdminUserAvatar", fields: [avatarId], references: [id])
  Media                 Media?         @relation(fields: [mediaId], references: [id])
  mediaId               String?
  assignedContacts      Contact[]      @relation("ContactAssignedTo")
  contactNotes          ContactNote[]  @relation("ContactNoteAdmin")

  @@map("admin_users")
}

// Category model
model Category {
  id          String   @id @default(cuid())
  name        String
  description String?
  slug        String   @unique
  imageId     String? // Reference to Media table
  parentId    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")
  products Product[]
  posts    Post[]
  image    Media?     @relation("CategoryImage", fields: [imageId], references: [id])
  Media    Media?     @relation(fields: [mediaId], references: [id])
  mediaId  String?

  @@map("categories")
}

// Brand model
model Brand {
  id          String   @id @default(cuid())
  name        String
  description String?
  slug        String   @unique
  logoId      String? // Reference to Media table
  website     String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  products Product[]
  logo     Media?    @relation("BrandLogo", fields: [logoId], references: [id])
  Media    Media?    @relation(fields: [mediaId], references: [id])
  mediaId  String?

  @@map("brands")
}

// Attribute model
model Attribute {
  id           String        @id @default(cuid())
  name         String
  slug         String        @unique
  description  String?
  type         AttributeType @default(TEXT)
  isRequired   Boolean       @default(false)
  isFilterable Boolean       @default(true)
  sortOrder    Int           @default(0)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt

  // Relations
  values   AttributeValue[]
  products ProductAttribute[]

  @@map("attributes")
}

// Attribute Value model
model AttributeValue {
  id          String   @id @default(cuid())
  attributeId String
  value       String
  slug        String
  sortOrder   Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  attribute Attribute          @relation(fields: [attributeId], references: [id], onDelete: Cascade)
  products  ProductAttribute[]

  @@unique([attributeId, slug])
  @@map("attribute_values")
}

// Product Attribute (Many-to-Many relationship)
model ProductAttribute {
  id               String   @id @default(cuid())
  productId        String
  attributeId      String
  attributeValueId String
  createdAt        DateTime @default(now())

  // Relations
  product        Product        @relation(fields: [productId], references: [id], onDelete: Cascade)
  attribute      Attribute      @relation(fields: [attributeId], references: [id], onDelete: Cascade)
  attributeValue AttributeValue @relation(fields: [attributeValueId], references: [id], onDelete: Cascade)

  @@unique([productId, attributeId])
  @@map("product_attributes")
}

enum AttributeType {
  TEXT
  NUMBER
  COLOR
  SIZE
  BOOLEAN
  SELECT
  MULTI_SELECT
}

// Product model
model Product {
  id          String  @id @default(cuid())
  name        String
  description String
  price       Float
  salePrice   Float?
  categoryId  String
  brandId     String?

  stock       Int           @default(0)
  sku         String        @unique
  slug        String        @unique
  featured    Boolean       @default(false)
  status      ProductStatus @default(ACTIVE)
  tags        String[]
  avgRating   Float         @default(0)
  reviewCount Int           @default(0)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  category Category @relation(fields: [categoryId], references: [id])
  brand    Brand?   @relation(fields: [brandId], references: [id])

  cartItems        CartItem[]
  orderItems       OrderItem[]
  reviews          Review[]
  inventoryEntries InventoryEntry[]
  ProductAttribute ProductAttribute[]
  WishlistItem     WishlistItem[]
  media            ProductMedia[] // Images through Media table

  @@map("products")
}

// Cart model
model Cart {
  id        String   @id @default(cuid())
  userId    String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user  User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  items CartItem[]

  @@map("carts")
}

// Cart Item model
model CartItem {
  id        String   @id @default(cuid())
  cartId    String
  productId String
  quantity  Int
  price     Float
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  cart    Cart    @relation(fields: [cartId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([cartId, productId])
  @@map("cart_items")
}

// Order model
model Order {
  id              String        @id @default(cuid())
  userId          String
  total           Float
  status          OrderStatus   @default(PENDING)
  paymentMethod   PaymentMethod @default(COD)
  paymentStatus   PaymentStatus @default(PENDING)
  shippingAddress Json
  billingAddress  Json?
  notes           String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  user               User                 @relation(fields: [userId], references: [id])
  items              OrderItem[]
  PaymentTransaction PaymentTransaction[]

  @@map("orders")
}

// Order Item model
model OrderItem {
  id        String   @id @default(cuid())
  orderId   String
  productId String
  quantity  Int
  price     Float
  total     Float
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

// Address model
model Address {
  id        String   @id @default(cuid())
  userId    String
  fullName  String
  phone     String
  address   String
  ward      String
  district  String
  province  String
  isDefault Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("addresses")
}

// Review model
model Review {
  id        String       @id @default(cuid())
  userId    String
  productId String
  rating    Int          @default(5)
  comment   String?
  status    ReviewStatus @default(PENDING)
  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt

  // Relations
  user    User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product       @relation(fields: [productId], references: [id], onDelete: Cascade)
  media   ReviewMedia[] // Images through Media table

  @@unique([userId, productId])
  @@map("reviews")
}

model Post {
  id              String     @id @default(cuid())
  title           String
  content         String
  excerpt         String?
  slug            String     @unique
  status          PostStatus @default(DRAFT)
  featured        Boolean    @default(false)
  featuredImageId String? // Reference to Media table
  tags            String[]
  categoryId      String?
  authorId        String
  viewCount       Int        @default(0)
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // Relations
  category      Category? @relation(fields: [categoryId], references: [id])
  author        AdminUser @relation(fields: [authorId], references: [id])
  featuredImage Media?    @relation("PostFeaturedImage", fields: [featuredImageId], references: [id])
  Media         Media?    @relation(fields: [mediaId], references: [id])
  mediaId       String?

  @@map("posts")
}

model Page {
  id              String     @id @default(cuid())
  title           String
  content         String
  excerpt         String?
  slug            String     @unique
  status          PageStatus @default(DRAFT)
  featured        Boolean    @default(false)
  featuredImageId String? // Reference to Media table
  metaTitle       String?
  metaDescription String?
  authorId        String
  viewCount       Int        @default(0)
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // Relations
  author        AdminUser @relation(fields: [authorId], references: [id])
  featuredImage Media?    @relation("PageFeaturedImage", fields: [featuredImageId], references: [id])
  Media         Media?    @relation(fields: [mediaId], references: [id])
  mediaId       String?

  @@map("pages")
}

model Setting {
  id    String @id @default(cuid())
  key   String @unique
  value Json
  type  String @default("string") // string, number, boolean, json

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Inventory Entry model - Tracks stock for each product
model InventoryEntry {
  id        String   @id @default(cuid())
  productId String
  quantity  Int      @default(0)
  reserved  Int      @default(0) // Reserved for pending orders
  available Int      @default(0) // Available = quantity - reserved
  minStock  Int      @default(0) // Minimum stock level for alerts
  maxStock  Int? // Maximum stock level
  location  String? // Warehouse location
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  product        Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
  stockMovements StockMovement[]

  @@unique([productId])
  @@map("inventory_entries")
}

// Stock Movement model - Tracks all stock changes
model StockMovement {
  id               String            @id @default(cuid())
  inventoryEntryId String
  type             StockMovementType
  quantity         Int // Positive for IN, negative for OUT
  reason           String? // Reason for the movement
  reference        String? // Reference to order, return, etc.
  notes            String?
  createdBy        String? // Admin who made the change
  createdAt        DateTime          @default(now())

  // Relations
  inventoryEntry InventoryEntry @relation(fields: [inventoryEntryId], references: [id], onDelete: Cascade)

  @@map("stock_movements")
}

// Enums
enum AdminRole {
  ADMIN
  MODERATOR
}

enum Gender {
  MALE
  FEMALE
  OTHER
}

enum MediaType {
  INTERNAL
  EXTERNAL
}

// Media model - Centralized media management
model Media {
  id          String    @id @default(cuid())
  filename    String // Original filename
  path        String // File path in storage
  url         String // Full URL to access the file
  mimeType    String // MIME type (image/jpeg, image/png, etc.)
  size        Int // File size in bytes
  width       Int? // Image width (for images)
  height      Int? // Image height (for images)
  alt         String? // Alt text for accessibility
  title       String? // Media title
  description String? // Media description
  folder      String    @default("uploads") // Storage folder
  type        MediaType @default(INTERNAL) // Internal or external media
  externalUrl String? // External URL if type is EXTERNAL
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  productImages    ProductMedia[]
  brandLogos       Brand[]        @relation("BrandLogo")
  categoryImages   Category[]     @relation("CategoryImage")
  postImages       Post[]         @relation("PostFeaturedImage")
  pageImages       Page[]         @relation("PageFeaturedImage")
  eventImages      Event[]        @relation("EventImage")
  reviewImages     ReviewMedia[]
  userAvatars      User[]         @relation("UserAvatar")
  adminUserAvatars AdminUser[]    @relation("AdminUserAvatar")
  AdminUser        AdminUser[]
  Category         Category[]
  Brand            Brand[]
  Post             Post[]
  Page             Page[]
  Event            Event[]

  @@map("media")
}

// Product Media relationship - Many-to-Many between Product and Media
model ProductMedia {
  id        String   @id @default(cuid())
  productId String
  mediaId   String
  order     Int      @default(0) // Display order
  isPrimary Boolean  @default(false) // Primary image for product
  createdAt DateTime @default(now())

  // Relations
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  media   Media   @relation(fields: [mediaId], references: [id], onDelete: Cascade)

  @@unique([productId, mediaId])
  @@map("product_media")
}

// Review Media relationship - Many-to-Many between Review and Media
model ReviewMedia {
  id       String @id @default(cuid())
  reviewId String
  mediaId  String
  order    Int    @default(0) // Display order

  // Relations
  review Review @relation(fields: [reviewId], references: [id], onDelete: Cascade)
  media  Media  @relation(fields: [mediaId], references: [id], onDelete: Cascade)

  @@unique([reviewId, mediaId])
  @@map("review_media")
}

enum ProductStatus {
  ACTIVE
  INACTIVE
  OUT_OF_STOCK
}

enum ReviewStatus {
  PENDING
  APPROVED
  REJECTED
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
}

enum PaymentMethod {
  COD
  BANK_TRANSFER
  CREDIT_CARD
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}

enum PostStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum PageStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum StockMovementType {
  IN // Stock increase (purchase, return, adjustment)
  OUT // Stock decrease (sale, damage, adjustment)
  TRANSFER // Stock transfer between locations
  ADJUSTMENT // Manual adjustment
}

// Notification model - Admin notifications system
model Notification {
  id         String               @id @default(cuid())
  title      String
  message    String
  type       NotificationType     @default(INFO)
  priority   NotificationPriority @default(NORMAL)
  targetType NotificationTarget   @default(ALL_ADMINS)
  targetId   String? // Specific admin user ID if targeted
  isRead     Boolean              @default(false)
  readAt     DateTime?
  actionUrl  String? // URL to navigate when clicked
  metadata   Json? // Additional data for the notification
  expiresAt  DateTime? // Optional expiration date
  createdBy  String? // Admin who created the notification
  createdAt  DateTime             @default(now())
  updatedAt  DateTime             @updatedAt

  // Relations
  creator AdminUser? @relation("NotificationCreator", fields: [createdBy], references: [id])
  target  AdminUser? @relation("NotificationTarget", fields: [targetId], references: [id])

  @@map("notifications")
}

// Audit Log model - Track admin actions
model AuditLog {
  id          String   @id @default(cuid())
  action      String // Action performed (CREATE, UPDATE, DELETE, etc.)
  resource    String // Resource type (Product, User, Order, etc.)
  resourceId  String? // ID of the affected resource
  oldValues   Json? // Previous values (for updates)
  newValues   Json? // New values (for creates/updates)
  description String? // Human-readable description
  ipAddress   String? // IP address of the admin
  userAgent   String? // User agent string
  adminId     String // Admin who performed the action
  createdAt   DateTime @default(now())

  // Relations
  admin AdminUser @relation("AuditLogAdmin", fields: [adminId], references: [id])

  @@map("audit_logs")
}

// Notification enums
enum NotificationType {
  INFO
  SUCCESS
  WARNING
  ERROR
  SYSTEM
}

enum NotificationPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum NotificationTarget {
  ALL_ADMINS
  SPECIFIC_ADMIN
  ROLE_ADMIN
  ROLE_MODERATOR
}

// Menu model for navigation management
model Menu {
  id          String   @id @default(cuid())
  name        String // Menu name (e.g., "Main Menu", "Footer Menu")
  location    String // Menu location (e.g., "header", "footer", "sidebar")
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  items MenuItem[]

  @@map("menus")
}

// Menu Item model for individual menu entries
model MenuItem {
  id        String       @id @default(cuid())
  menuId    String
  parentId  String? // For nested menu items
  title     String // Display text
  url       String? // Link URL
  type      MenuItemType @default(LINK)
  target    String? // Link target (_blank, _self, etc.)
  icon      String? // Icon class or URL
  cssClass  String? // Custom CSS classes
  order     Int          @default(0) // Sort order
  isActive  Boolean      @default(true)
  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt

  // Relations
  menu     Menu       @relation(fields: [menuId], references: [id], onDelete: Cascade)
  parent   MenuItem?  @relation("MenuItemHierarchy", fields: [parentId], references: [id])
  children MenuItem[] @relation("MenuItemHierarchy")

  @@map("menu_items")
}

enum MenuItemType {
  LINK // Regular link
  PAGE // Link to a page
  CATEGORY // Link to a category
  PRODUCT // Link to a product
  CUSTOM // Custom content
  SEPARATOR // Visual separator
}

// Event model for event management
model Event {
  id               String      @id @default(cuid())
  title            String
  description      String?
  content          String? // Rich text content
  slug             String      @unique
  type             EventType   @default(PROMOTION)
  status           EventStatus @default(DRAFT)
  startDate        DateTime
  endDate          DateTime?
  isAllDay         Boolean     @default(false)
  location         String? // Physical or virtual location
  maxAttendees     Int? // Maximum number of attendees
  currentAttendees Int         @default(0)
  price            Float? // Event price (null for free events)
  currency         String      @default("VND")
  imageId          String? // Reference to Media table
  tags             String[] // Event tags for categorization
  metadata         Json? // Additional event metadata
  seoTitle         String?
  seoDescription   String?
  createdBy        String // Admin who created the event
  createdAt        DateTime    @default(now())
  updatedAt        DateTime    @updatedAt

  // Relations
  creator AdminUser @relation("EventCreator", fields: [createdBy], references: [id])
  image   Media?    @relation("EventImage", fields: [imageId], references: [id])
  Media   Media?    @relation(fields: [mediaId], references: [id])
  mediaId String?

  @@map("events")
}

enum EventType {
  PROMOTION // Sales, discounts, special offers
  LAUNCH // Product launches
  WORKSHOP // Educational workshops
  WEBINAR // Online seminars
  SALE // Flash sales, clearance
  SEASONAL // Holiday, seasonal events
  COMMUNITY // Community events
  OTHER // Other types
}

enum EventStatus {
  DRAFT // Not published yet
  PUBLISHED // Live and visible
  SCHEDULED // Scheduled for future
  ONGOING // Currently happening
  COMPLETED // Event has ended
  CANCELLED // Event cancelled
}

// Payment Gateway Configuration
model PaymentGateway {
  id               String          @id @default(cuid())
  name             String // Gateway name (e.g., "VNPay", "MoMo", "ZaloPay")
  provider         PaymentProvider
  isActive         Boolean         @default(false)
  isDefault        Boolean         @default(false)
  config           Json // Gateway-specific configuration
  credentials      Json // Encrypted credentials
  supportedMethods String[] // Supported payment methods
  fees             Json? // Fee structure
  limits           Json? // Transaction limits
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @updatedAt

  // Relations
  transactions PaymentTransaction[]

  @@map("payment_gateways")
}

// Payment Transaction Log
model PaymentTransaction {
  id              String                   @id @default(cuid())
  orderId         String? // Related order ID
  gatewayId       String // Payment gateway used
  externalId      String? // Gateway transaction ID
  amount          Float
  currency        String                   @default("VND")
  method          PaymentMethod
  status          PaymentTransactionStatus @default(PENDING)
  gatewayResponse Json? // Raw gateway response
  failureReason   String? // Failure reason if failed
  processedAt     DateTime? // When payment was processed
  refundedAt      DateTime? // When payment was refunded
  refundAmount    Float? // Refunded amount
  metadata        Json? // Additional metadata
  createdAt       DateTime                 @default(now())
  updatedAt       DateTime                 @updatedAt

  // Relations
  gateway PaymentGateway @relation(fields: [gatewayId], references: [id])
  order   Order?         @relation(fields: [orderId], references: [id])

  @@map("payment_transactions")
}

enum PaymentProvider {
  VNPAY
  MOMO
  ZALOPAY
  PAYPAL
  STRIPE
  BANK_TRANSFER
  COD
  CUSTOM
}

enum PaymentTransactionStatus {
  PENDING
  PROCESSING
  SUCCESS
  FAILED
  CANCELLED
  REFUNDED
  PARTIAL_REFUND
}

// Promotion model for discount codes and promotions
model Promotion {
  id                   String        @id @default(cuid())
  name                 String // Promotion name
  description          String? // Promotion description
  code                 String        @unique // Discount code
  type                 PromotionType @default(PERCENTAGE)
  value                Float // Discount value (percentage or fixed amount)
  minOrderAmount       Float? // Minimum order amount to apply
  maxDiscountAmount    Float? // Maximum discount amount (for percentage type)
  usageLimit           Int? // Maximum number of uses (null = unlimited)
  usageCount           Int           @default(0) // Current usage count
  userUsageLimit       Int? // Maximum uses per user (null = unlimited)
  startDate            DateTime // Promotion start date
  endDate              DateTime? // Promotion end date (null = no expiry)
  isActive             Boolean       @default(true)
  applicableProducts   String[] // Product IDs (empty = all products)
  applicableCategories String[] // Category IDs (empty = all categories)
  excludedProducts     String[] // Excluded product IDs
  excludedCategories   String[] // Excluded category IDs
  createdBy            String // Admin who created the promotion
  createdAt            DateTime      @default(now())
  updatedAt            DateTime      @updatedAt

  @@map("promotions")
}

enum PromotionType {
  PERCENTAGE // Percentage discount
  FIXED_AMOUNT // Fixed amount discount
  FREE_SHIPPING // Free shipping
  BUY_X_GET_Y // Buy X get Y free
}

// Shipping Zone model for shipping configuration
model ShippingZone {
  id          String   @id @default(cuid())
  name        String // Zone name (e.g., "Hà Nội", "TP.HCM", "Miền Bắc")
  description String? // Zone description
  provinces   String[] // List of province codes/names
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  methods ShippingMethod[]

  @@map("shipping_zones")
}

// Shipping Method model
model ShippingMethod {
  id              String             @id @default(cuid())
  name            String // Method name (e.g., "Giao hàng tiêu chuẩn", "Giao hàng nhanh")
  description     String? // Method description
  type            ShippingMethodType @default(STANDARD)
  baseFee         Float // Base shipping fee
  freeShippingMin Float? // Minimum order value for free shipping
  estimatedDays   String // Estimated delivery time (e.g., "2-3 ngày")
  maxWeight       Float? // Maximum weight (kg)
  maxDimensions   Json? // Maximum dimensions {length, width, height}
  isActive        Boolean            @default(true)
  sortOrder       Int                @default(0)
  zoneId          String
  createdAt       DateTime           @default(now())
  updatedAt       DateTime           @updatedAt

  // Relations
  zone ShippingZone @relation(fields: [zoneId], references: [id], onDelete: Cascade)

  @@map("shipping_methods")
}

enum ShippingMethodType {
  STANDARD // Standard delivery
  EXPRESS // Express delivery
  SAME_DAY // Same day delivery
  PICKUP // Store pickup
  CUSTOM // Custom method
}

// Email Template model for email management
model EmailTemplate {
  id        String            @id @default(cuid())
  name      String // Template name
  subject   String // Email subject
  content   String // Email content (HTML)
  type      EmailTemplateType @default(CUSTOM)
  variables Json? // Available variables for template
  isActive  Boolean           @default(true)
  isDefault Boolean           @default(false) // Default template for type
  createdBy String // Admin who created the template
  createdAt DateTime          @default(now())
  updatedAt DateTime          @updatedAt

  // Relations
  emailLogs EmailLog[]

  @@map("email_templates")
}

// Email Log model for tracking sent emails
model EmailLog {
  id         String      @id @default(cuid())
  templateId String?
  recipient  String // Email recipient
  subject    String // Email subject
  content    String // Email content (HTML)
  status     EmailStatus @default(PENDING)
  error      String? // Error message if failed
  sentAt     DateTime?
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt

  // Relations
  template EmailTemplate? @relation(fields: [templateId], references: [id])

  @@map("email_logs")
}

// SMTP Configuration model
model SMTPConfig {
  id        String   @id @default(cuid())
  name      String // Configuration name
  host      String // SMTP host
  port      Int // SMTP port
  secure    Boolean  @default(true) // Use SSL/TLS
  username  String // SMTP username
  password  String // SMTP password (encrypted)
  fromName  String // Default sender name
  fromEmail String // Default sender email
  isActive  Boolean  @default(false)
  isDefault Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("smtp_configs")
}

enum EmailTemplateType {
  WELCOME // Welcome email
  ORDER_CONFIRMATION // Order confirmation
  ORDER_SHIPPED // Order shipped notification
  ORDER_DELIVERED // Order delivered notification
  PASSWORD_RESET // Password reset
  NEWSLETTER // Newsletter
  PROMOTION // Promotional email
  CUSTOM // Custom template
}

enum EmailStatus {
  PENDING // Waiting to be sent
  SENT // Successfully sent
  FAILED // Failed to send
  BOUNCED // Email bounced
}

// SEO Settings model for search engine optimization
model SEOSettings {
  id                 String   @id @default(cuid())
  // Global SEO settings
  siteName           String? // Site name
  siteDescription    String? // Site description
  siteKeywords       String[] // Site keywords
  defaultTitle       String? // Default page title
  titleTemplate      String? // Title template (e.g., "%s | Site Name")
  defaultDescription String? // Default meta description
  defaultImage       String? // Default OG image

  // Social media settings
  ogSiteName     String? // Open Graph site name
  ogType         String? // Open Graph type
  twitterSite    String? // Twitter site handle
  twitterCreator String? // Twitter creator handle

  // Technical SEO
  robotsTxt        String? // robots.txt content
  sitemapEnabled   Boolean @default(true) // Enable sitemap generation
  sitemapFrequency String? @default("weekly") // Sitemap update frequency

  // Analytics
  googleAnalyticsId  String? // Google Analytics tracking ID
  googleTagManagerId String? // Google Tag Manager ID
  facebookPixelId    String? // Facebook Pixel ID

  // Schema.org settings
  organizationName String? // Organization name
  organizationLogo String? // Organization logo
  organizationType String? // Organization type
  contactEmail     String? // Contact email
  contactPhone     String? // Contact phone
  address          Json? // Organization address

  // Search Console
  googleSiteVerification String? // Google Search Console verification
  bingSiteVerification   String? // Bing Webmaster verification

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("seo_settings")
}

// Page SEO model for individual page SEO settings
model PageSEO {
  id                 String   @id @default(cuid())
  path               String   @unique // Page path (e.g., "/", "/about", "/products/[slug]")
  title              String? // Page title
  description        String? // Meta description
  keywords           String[] // Page keywords
  ogTitle            String? // Open Graph title
  ogDescription      String? // Open Graph description
  ogImage            String? // Open Graph image
  ogType             String? // Open Graph type
  twitterTitle       String? // Twitter title
  twitterDescription String? // Twitter description
  twitterImage       String? // Twitter image
  twitterCard        String? // Twitter card type
  canonical          String? // Canonical URL
  noindex            Boolean  @default(false) // Prevent indexing
  nofollow           Boolean  @default(false) // Prevent following links
  priority           Float?   @default(0.5) // Sitemap priority (0.0 - 1.0)
  changefreq         String?  @default("weekly") // Sitemap change frequency
  isActive           Boolean  @default(true)
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  @@map("page_seo")
}

// Contact model for contact form submissions
model Contact {
  id              String          @id @default(cuid())
  name            String // Full name
  email           String // Email address
  phone           String? // Phone number
  company         String? // Company/shop name
  service         String? // Service interested in
  subject         String? // Message subject
  message         String // Message content
  status          ContactStatus   @default(NEW)
  priority        ContactPriority @default(NORMAL)
  source          String          @default("website") // Source of contact (website, phone, email, etc.)
  ipAddress       String? // IP address of sender
  userAgent       String? // User agent string
  assignedTo      String? // Admin user assigned to handle this contact
  respondedAt     DateTime? // When response was sent
  responseMessage String? // Admin response message
  tags            String[] // Tags for categorization
  metadata        Json? // Additional metadata
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  // Relations
  assignedAdmin AdminUser?    @relation("ContactAssignedTo", fields: [assignedTo], references: [id])
  notes         ContactNote[]

  @@map("contacts")
}

// Contact Notes model for admin notes on contacts
model ContactNote {
  id         String   @id @default(cuid())
  contactId  String
  adminId    String
  note       String
  isInternal Boolean  @default(true) // Internal note or customer-visible
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  contact Contact   @relation(fields: [contactId], references: [id], onDelete: Cascade)
  admin   AdminUser @relation("ContactNoteAdmin", fields: [adminId], references: [id])

  @@map("contact_notes")
}

enum ContactStatus {
  NEW // New contact, not yet reviewed
  OPEN // Contact is being handled
  IN_PROGRESS // Work in progress
  WAITING_CUSTOMER // Waiting for customer response
  RESOLVED // Contact resolved
  CLOSED // Contact closed
  SPAM // Marked as spam
}

enum ContactPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}
